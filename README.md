# Property Agent

A CLI-based property management agent that connects to MCP server via JSON-RPC using LangChain framework.

## Features

- **Property Management**: List and search properties with filtering
- **Unit Management**: Browse units by property, type, and availability
- **User Management**: Search and manage tenants, owners, and staff
- **Invoice Management**: Track and filter invoices by various criteria
- **Tower & Floor Management**: Navigate building structures
- **MCP Server Integration**: Connects to property management system via MCP protocol

## Installation

1. Create and activate a virtual environment:
```bash
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Configuration

### LLM Provider Setup

The Property Agent supports multiple LLM providers:

**OpenAI (Default)**
```bash
python setup_openai.py
```

**DeepSeek (Alternative)**
```bash
python setup_deepseek.py
```

**Manual Configuration**
```bash
cp .env .env.local
# Edit .env.local with your actual values
```

See [DEEPSEEK_SETUP.md](DEEPSEEK_SETUP.md) for detailed DeepSeek integration guide.

## Usage

### Basic Usage
```bash
python main.py
```

### With Custom Server URL
```bash
python main.py --server-url http://your-server:8080 --timeout 60
```

### Available Commands

The agent supports natural language queries for:

- **Property Queries**: "list properties", "find properties with NUSAN", "show property details for ID 123"
- **Unit Queries**: "list units in property 123", "find available units", "show unit details"
- **User Queries**: "list tenants", "find user by name John", "show user details"
- **Invoice Queries**: "list invoices", "find unpaid invoices", "show invoice details"

### Tool Input Formats

Tools accept pipe-separated parameters:

- **Property List**: `search_term|page=2|limit=5`
- **Unit List**: `property_id|unit_type|page=2|limit=5`
- **Tower List**: `property_id|page=2|limit=5`
- **Floor List**: `tower_id|property_id|page=2|limit=5`
- **User List**: `search_term|user_type|page=2|limit=5`
- **Invoice List**: `user_id|property_id|status|page=2|limit=5`

## MCP Server API

The agent connects to an MCP server with the following endpoints:

### Health Check
```bash
curl -X GET http://localhost:8080/health
```

### Tool Call
```bash
curl -X POST http://localhost:8080 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "property_list",
      "arguments": {
        "property_code": "NUSAN",
        "page": 1,
        "limit": 10
      }
    },
    "id": 1
  }'
```

## Available Tools

1. **property_list** - List properties with optional filtering
2. **property_get_by_id** - Get detailed property information
3. **unit_list** - List units with filtering options
4. **unit_get** - Get detailed unit information
5. **tower_list** - List towers/buildings
6. **floor_list** - List floors in towers
7. **user_list** - List users/tenants with filtering
8. **user_get_by_id** - Get detailed user information
9. **invoice_list** - List invoices with filtering
10. **invoice_get** - Get detailed invoice information

## Testing

Run the test suite:
```bash
python -m pytest test_main.py -v
```

Test MCP connection directly:
```bash
python test_mcp_connection.py
```

## Development

### Code Structure

- `main.py` - Main application with agent and tools
- `test_main.py` - Comprehensive test suite
- `test_mcp_connection.py` - MCP server connection testing
- `requirements.txt` - Python dependencies
- `.env` - Environment configuration template

### Key Components

- **MCPClient** - Handles JSON-RPC communication with MCP server
- **PropertyAgent** - Main agent class with LangChain integration
- **Tool Classes** - Individual tools for different property management functions
- **MockLLM** - Placeholder LLM (replace with real implementation)

### Adding Real LLM Support

Replace the MockLLM with a real LLM implementation:

```python
from langchain.llms import OpenAI  # or other LLM

# In PropertyAgent.__init__:
self.llm = llm or OpenAI(api_key="your-api-key")
```

## Known Issues

1. **MockLLM Limitation**: The current MockLLM causes parsing errors. Replace with a real LLM for full functionality.
2. **LangChain Deprecations**: Uses older LangChain patterns. Consider migrating to LangGraph for new features.
3. **MCP Server Compatibility**: Ensure your MCP server implements the expected tool endpoints.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]
