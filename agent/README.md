# Property Agent

A modern property management assistant built with Pydantic-AI that provides natural language access to property management functions via the PMS Gateway MCP Server.

## 🏗️ Architecture

This directory contains two implementations of the property agent:

- **`prototype/`** - Original prototype using basic Pydantic-AI patterns (reference implementation)
- **`pydantic_ai/`** - Production-ready implementation with full MCP integration

## 🚀 Quick Start

### Prerequisites

1. **Install Dependencies**:
   ```bash
   pip install -r ../requirements.txt
   ```

2. **Set up API Key**:
   ```bash
   # For OpenAI
   export OPENAI_API_KEY='your_openai_api_key_here'

   # For DeepSeek (uses OpenAI-compatible API)
   export OPENAI_API_KEY='your_deepseek_api_key_here'

   # For Gemini
   export GEMINI_API_KEY='your_gemini_api_key_here'

   # For OpenRouter (Claude, <PERSON>lama, Mistral, etc.)
   export OPENROUTER_API_KEY='your_openrouter_api_key_here'

   # For Together AI (<PERSON>wen, CodeLlama, Mixtral, etc.)
   export TOGETHER_API_KEY='your_together_api_key_here'
   ```

3. **Start MCP Server**:
   Ensure the PMS Gateway MCP Server is running on `localhost:8080`

### Running Cikal (Interactive Mode)

```bash
cd agent/pydantic_ai
python -m main
```

### Running Single Commands

```bash
# Basic command
python -m main -c "find properties in NUSAN"

# With default property
python -m main -p NUSA3 -c "show me the facilities"

# Using DeepSeek model
python -m main --model deepseek -c "list invoices"
```

## 🛠️ Features

### Core Capabilities

- **Natural Language Interface**: Chat with Cikal using everyday language
- **Property Management**: Search and manage properties
- **User Management**: Look up user details and information
- **Facility Booking**: View available facilities and booking options
- **Financial Records**: Access invoices and financial data
- **Generic Tool Access**: Call any of the 59+ MCP server tools

### Technical Features

- **Pydantic-AI Framework**: Modern, type-safe agent framework
- **MCP Integration**: Full JSON-RPC 2.0 communication with PMS Gateway
- **Multiple LLM Support**: OpenAI GPT-4o, DeepSeek, and other OpenAI-compatible models
- **Error Handling**: Graceful error handling with user-friendly messages
- **Dependency Injection**: Clean architecture with proper separation of concerns
- **Async Support**: Full async/await support for better performance

## 📁 Project Structure

```
agent/
├── pydantic_ai/
│   ├── __init__.py          # Package initialization
│   ├── agent.py             # Main PropertyAgent class
│   ├── tools.py             # Tool definitions and MCP integration
│   ├── models.py            # Pydantic models for data structures
│   ├── mcp_client.py        # MCP server communication client
│   └── main.py              # CLI interface
├── prototype/               # Original prototype implementation
│   ├── code.txt            # Prototype code
│   └── technical.md        # Technical documentation
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables

- `OPENAI_API_KEY` - API key for OpenAI or DeepSeek
- `GEMINI_API_KEY` - API key for Google Gemini models
- `OPENROUTER_API_KEY` - API key for OpenRouter (Claude, Llama, Mistral, etc.)
- `TOGETHER_API_KEY` - API key for Together AI (Qwen, CodeLlama, Mixtral, etc.)
- `MCP_SERVER_URL` - MCP server URL (default: http://localhost:8080)

### Command Line Options

```bash
python -m main [OPTIONS]

Options:
  -c, --command TEXT     Execute a single command and exit
  -p, --property TEXT    Default property code to use
  --model TEXT          LLM model (default: openai:gpt-4o, also: deepseek, gemini, claude, llama, mistral, qwen, codellama, mixtral)
  --api-key TEXT        API key (overrides environment variable)
  --mcp-url TEXT        MCP server URL (default: http://localhost:8080)
  --version             Show version and exit
  --help                Show help message
```

### Supported Models

| Model | API Key Required | Example Usage |
|-------|------------------|---------------|
| **OpenAI GPT-4o** | `OPENAI_API_KEY` | `--model openai:gpt-4o` (default) |
| **OpenAI GPT-4** | `OPENAI_API_KEY` | `--model openai:gpt-4` |
| **DeepSeek Chat** | `OPENAI_API_KEY` | `--model deepseek` or `--model openai:deepseek-chat` |
| **Gemini 1.5 Flash** | `GEMINI_API_KEY` | `--model gemini` or `--model gemini-1.5-flash` |
| **Gemini 1.5 Pro** | `GEMINI_API_KEY` | `--model gemini-1.5-pro` |
| **Claude 3.5 Sonnet** | `OPENROUTER_API_KEY` | `--model claude` or `--model openrouter:anthropic/claude-3.5-sonnet` |
| **Llama 3.1 8B** | `OPENROUTER_API_KEY` | `--model llama` or `--model openrouter:meta-llama/llama-3.1-8b-instruct` |
| **Mistral 7B** | `OPENROUTER_API_KEY` | `--model mistral` or `--model openrouter:mistralai/mistral-7b-instruct` |
| **Qwen 2.5 7B** | `TOGETHER_API_KEY` | `--model qwen` or `--model together:Qwen/Qwen2.5-7B-Instruct` |
| **CodeLlama 7B** | `TOGETHER_API_KEY` | `--model codellama` or `--model together:codellama/CodeLlama-7b-Instruct-hf` |
| **Mixtral 8x7B** | `TOGETHER_API_KEY` | `--model mixtral` or `--model together:mistralai/Mixtral-8x7B-Instruct-v0.1` |

**Examples:**
```bash
# Use OpenAI GPT-4o (default)
python main.py -c "Hello Cikal"

# Use DeepSeek
python main.py --model deepseek -c "Hello Cikal"

# Use Gemini
python main.py --model gemini -c "Hello Cikal"

# Use specific Gemini model
python main.py --model gemini-1.5-pro -c "Hello Cikal"

# Use Claude via OpenRouter
python main.py --model claude -c "Hello Cikal"

# Use Llama via OpenRouter
python main.py --model llama -c "Hello Cikal"

# Use Mistral via OpenRouter
python main.py --model mistral -c "Hello Cikal"

# Use specific OpenRouter model
python main.py --model openrouter:anthropic/claude-3.5-sonnet -c "Hello Cikal"

# Use Qwen via Together AI
python main.py --model qwen -c "Hello Cikal"

# Use CodeLlama via Together AI
python main.py --model codellama -c "Hello Cikal"

# Use Mixtral via Together AI
python main.py --model mixtral -c "Hello Cikal"

# Use specific Together AI model
python main.py --model together:Qwen/Qwen2.5-7B-Instruct -c "Hello Cikal"
```

## 🎯 Example Conversations

### Property Search
```
You: find me some properties
Cikal: I'd be happy to help you find properties! To give you the most relevant results, 
      could you let me know which property code you're interested in? For example, 
      'NUSAN', 'NUSA3', or 'EMBRL'?

You: show me properties in NUSAN
Cikal: I found 3 properties in NUSAN:

• Nusantara Residence (NUSAN)
  Address: 123 Marina Bay, Singapore
  Description: Luxury waterfront residences

• Nusantara Tower A (NUSAN-A)
  Address: 125 Marina Bay, Singapore
  
• Nusantara Tower B (NUSAN-B)
  Address: 127 Marina Bay, Singapore
```

### Facility Booking
```
You: what facilities are available at NUSA3?
Cikal: Available facilities at NUSA3:

• Swimming Pool
  Type: Recreation
  Description: Olympic-size swimming pool with lane swimming
  Available: Yes

• Badminton Court 1
  Type: Sports
  Available: Yes

• Gymnasium
  Type: Fitness
  Description: Fully equipped fitness center
  Available: Yes
```

### User Management
```
You: look <NAME_EMAIL> at property NUSAN
Cikal: User Information:
• Name: John Doe
• Email: <EMAIL>
• ID: user_12345
• Property: NUSAN
```

## 🧪 Development

### Running Tests

```bash
# Install development dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/
```

### Adding New Tools

1. Define argument model in `models.py`:
```python
class NewToolArgs(BaseModel):
    property_code: str = Field(..., description="Property code")
    # Add other arguments
```

2. Implement tool function in `tools.py`:
```python
async def new_tool(ctx: RunContext[PropertyAgentDependencies], args: NewToolArgs) -> str:
    # Implementation
    pass
```

3. Register tool in `agent.py`:
```python
self.agent.tool(new_tool)
```

## 🔍 Troubleshooting

### Common Issues

1. **"Cannot connect to property management system"**
   - Ensure MCP server is running on localhost:8080
   - Check server health: `curl http://localhost:8080/health`

2. **"No API key provided"**
   - For OpenAI/DeepSeek: Set `OPENAI_API_KEY` environment variable
   - For Gemini: Set `GEMINI_API_KEY` environment variable
   - For OpenRouter (Claude/Llama/Mistral): Set `OPENROUTER_API_KEY` environment variable
   - For Together AI (Qwen/CodeLlama/Mixtral): Set `TOGETHER_API_KEY` environment variable
   - Or use `--api-key` command line option

3. **"Property code required"**
   - Most operations need a property code
   - Use `-p` option to set default property
   - Or specify property code in your request

### Debug Mode

For detailed logging, set environment variable:
```bash
export PYDANTIC_AI_DEBUG=1
```

## 📚 Documentation

- [Pydantic-AI Documentation](https://ai.pydantic.dev/)
- [MCP Protocol Specification](https://modelcontextprotocol.io/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add type hints for all functions
3. Include docstrings for public methods
4. Test your changes with the MCP server
5. Update documentation as needed

## 📄 License

This project is part of the Property Agent system. See the main project license for details.
