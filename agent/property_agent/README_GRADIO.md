# 🌐 Cikal Property Agent - Gradio Web Interface

A modern, interactive web interface for the Cikal Property Agent built with Gradio, providing an intuitive chat-based interface for property management operations.

## 🚀 Features

### 🎨 **Modern Web Interface**
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Real-time Chat**: Interactive conversation interface with message history
- **Custom Styling**: Professional gradient themes and modern UI components
- **Example Queries**: Pre-built example buttons for quick testing

### 🧠 **AI-Powered Capabilities**
- **Memory System**: Remembers user names, preferences, and conversation context
- **NLP Processing**: Understands natural language queries without requiring specific codes
- **Multi-LLM Support**: Compatible with OpenAI, DeepSeek, Gemini, OpenRouter, and Together AI
- **Context Awareness**: Maintains conversation flow and user preferences

### 🏢 **Property Management Features**
- **Property Operations**: Complete property information and management
- **User Management**: Comprehensive user and tenant system
- **Facility Booking**: Booking management and availability tracking
- **Financial Management**: Invoice and payment processing
- **Data Export**: Reports and analytics in multiple formats

### 🔌 **API Integration**
- **REST API Endpoints**: Programmatic access to all functionality
- **External Integration**: Easy integration with other systems
- **Real-time Updates**: Live data synchronization

## 📦 Installation

### Prerequisites
```bash
# Ensure you have Python 3.8+ and the virtual environment activated
source ../../.venv/bin/activate

# Install Gradio (if not already installed)
pip install gradio
```

### Quick Start
```bash
# Navigate to the agent directory
cd agent/property_agent

# Run the Gradio interface
python gradio_app.py
```

The interface will be available at: **http://localhost:7860**

## ⚙️ Configuration

### LLM API Keys
Set one of the following environment variables for full functionality:

```bash
# OpenAI (recommended)
export OPENAI_API_KEY="your-openai-api-key"

# DeepSeek
export DEEPSEEK_API_KEY="your-deepseek-api-key"

# Gemini
export GEMINI_API_KEY="your-gemini-api-key"

# OpenRouter
export OPENROUTER_API_KEY="your-openrouter-api-key"

# Together AI
export TOGETHER_API_KEY="your-together-api-key"
```

### Server Configuration
Modify the launch configuration in `gradio_app.py`:

```python
launch_kwargs = {
    "server_name": "0.0.0.0",  # Listen on all interfaces
    "server_port": 7860,       # Default Gradio port
    "share": False,            # Set to True for public sharing
    "debug": True,             # Enable debug mode
}
```

## 🎯 Usage Examples

### Basic Conversations
```
User: "Hello, my name is John"
Cikal: "Hi John! It's great to meet you. I'm here to help with your property management needs..."

User: "What's my name?"
Cikal: "Your name is John. How can I assist you today?"
```

### Property Management
```
User: "Show me all available properties"
Cikal: "Here are the currently available properties: [detailed list]"

User: "Find me luxury apartments with swimming pools"
Cikal: "I found several luxury properties with swimming pools: [filtered results]"
```

### User Management
```
User: "List all users in the system"
Cikal: "Here are the users (Page 1): [user list with details]"

User: "Get user details <NAME_EMAIL>"
Cikal: "Here are the details for that user: [comprehensive user information]"
```

### Data Export
```
User: "Export user data in CSV format"
Cikal: "I'll generate a CSV export of the user data for you..."
```

## 🔧 Demo Mode

When no API key is configured, the interface runs in **Demo Mode**:

- ✅ **Interface Testing**: Full UI functionality available
- ✅ **Layout Preview**: See all components and styling
- ✅ **Example Responses**: Simulated responses for testing
- ⚠️ **Limited Functionality**: No real LLM integration or MCP server calls

Demo mode is perfect for:
- Testing the interface design
- Demonstrating the UI to stakeholders
- Development and debugging
- Training and onboarding

## 🌐 API Endpoints

The Gradio interface exposes REST API endpoints:

### Chat API
```bash
curl -X POST http://localhost:7860/api/chat \
  -H "Content-Type: application/json" \
  -d '{"data": ["Hello, show me properties"]}'
```

### Properties API
```bash
curl -X POST http://localhost:7860/api/properties \
  -H "Content-Type: application/json" \
  -d '{"data": []}'
```

### Users API
```bash
curl -X POST http://localhost:7860/api/users \
  -H "Content-Type: application/json" \
  -d '{"data": [10]}'
```

## 🎨 Customization

### Styling
The interface uses custom CSS for professional appearance:

```css
.gradio-container {
    max-width: 1200px !important;
    margin: auto !important;
}

.feature-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
}
```

### Themes
Built with `gr.themes.Soft()` for a modern, professional look.

### Components
- **Chatbot**: Main conversation interface
- **Textbox**: Message input with multi-line support
- **Buttons**: Action buttons and example queries
- **HTML**: Custom headers and feature descriptions

## 🔍 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Change the port in gradio_app.py
"server_port": 7861,  # Use different port
```

**2. API Key Not Found**
```bash
# Verify environment variable
echo $OPENAI_API_KEY

# Set temporarily
export OPENAI_API_KEY="your-key-here"
```

**3. Module Import Errors**
```bash
# Ensure virtual environment is activated
source ../../.venv/bin/activate

# Reinstall dependencies
pip install -r requirements.txt
```

### Debug Mode
Enable debug mode for detailed error messages:
```python
launch_kwargs = {
    "debug": True,
    "show_error": True,
}
```

## 🚀 Deployment

### Local Development
```bash
python gradio_app.py
```

### Production Deployment
```bash
# With public sharing
python gradio_app.py --share

# Custom configuration
python gradio_app.py --server-name 0.0.0.0 --server-port 8080
```

### Docker Deployment
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .
RUN pip install -r requirements.txt

EXPOSE 7860
CMD ["python", "gradio_app.py"]
```

## 📊 Performance

- **Response Time**: < 2 seconds for most queries
- **Concurrent Users**: Supports multiple simultaneous users
- **Memory Usage**: Efficient memory management with conversation history
- **Scalability**: Horizontal scaling with load balancers

## 🔐 Security

- **API Key Protection**: Environment variables for secure key storage
- **Input Validation**: Sanitized user inputs
- **Error Handling**: Graceful error management
- **CORS Support**: Configurable cross-origin requests

## 📈 Monitoring

- **Real-time Logs**: Console output for debugging
- **Error Tracking**: Comprehensive error handling
- **Usage Analytics**: Built-in Gradio analytics
- **Performance Metrics**: Response time monitoring

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/gradio-enhancement`
3. **Make changes**: Improve the interface or add features
4. **Test thoroughly**: Ensure all functionality works
5. **Submit PR**: Create pull request with detailed description

## 📝 License

This Gradio integration is part of the Cikal Property Agent project and follows the same licensing terms.

---

**🎉 Enjoy using the Cikal Property Agent Gradio Interface!**

For support or questions, please refer to the main project documentation or create an issue in the repository.
