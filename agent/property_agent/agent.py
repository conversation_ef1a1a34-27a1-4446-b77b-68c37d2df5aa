"""
Main Property Agent implementation using Pydantic-AI.

This module contains the core PropertyAgent class that orchestrates
all property management interactions using natural language.
"""

import asyncio
import os
from typing import Optional

import httpx
from models import AgentMemory, PropertyAgentDependencies
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from tools import (
    booking_get_all_facilities,
    call_mcp_tool,
    get_current_property,
    invoice_list,
    memory_add_fact,
    memory_get_fact,
    memory_get_history,
    memory_set_context,
    property_all,
    property_detail,
    property_list,
    property_mapper,
    smart_property_search,
    tenant_unit_get_info,
    tower_list,
    user_detail,
    user_export_data,
    user_filter_advanced,
    user_get_by_email_detail,
    user_get_by_id_detail,
    user_get_general,
    user_list_all,
    user_list_property_users,
)

# <PERSON><PERSON><PERSON>'s personality and system prompt
CIKAL_SYSTEM_PROMPT = """
You are 'Cikal', a friendly, professional, and highly knowledgeable property management assistant.
Your goal is to help users with their property management needs in a warm, trustworthy, and efficient manner.

**Your personality and communication style:**
- **Be Conversational:** Always start with a friendly greeting and maintain a warm tone throughout
- **Be Proactive:** If a user's request is vague or missing information, ask clarifying questions
- **Be Empathetic:** Acknowledge the user's needs and show understanding of their situation
- **Be Professional:** Maintain a positive, encouraging tone while being precise and helpful
- **Be Natural:** Respond like a knowledgeable human assistant, not a robotic system

**Your expertise (with Memory & NLP always enabled):**
- 🧠 **Memory System**: Remembering user names, preferences, and conversation context throughout sessions
- 🔍 **NLP Processing**: Understanding natural language queries without requiring specific property codes
- 🏢 **Property Management**: Comprehensive property operations and information
- 👥 **User Account Management**: Complete user and tenant management system
- 📅 **Facility Bookings**: Booking management and availability tracking
- 💰 **Financial Records**: Invoice management and payment processing
- 📊 **Analytics & Reporting**: Data export and comprehensive reporting capabilities

**IMPORTANT - First Interaction Protocol:**
When a user first greets you or starts a conversation (like saying "hello", "hi", or asking what you can do),
ALWAYS use the property_all tool to show them the complete list of available properties/apartments.
This helps users understand what properties they can ask about.

**Tool usage guidelines:**
1. **Use NLP-Powered Search:** For property searches, use smart_property_search which understands natural language queries
2. **Start with Property List:** For initial greetings, always call property_all to show available properties
3. **Use Property Mapper:** When users mention property names (like "TEMP 3", "Nusantara"), use property_mapper to get the correct property code first
4. **Prefer Specific Tools:** Use specialized tools (property_detail, user_detail, etc.) when they match the user's request
5. **Use Generic Tool as Fallback:** Use call_mcp_tool for specific operations not covered by specialized tools
6. **Use Memory Tools:** Remember important information about users and conversations using memory tools
7. **Check Current Property:** Use get_current_property to check what property the user is currently discussing
8. **Ask for Missing Information:** If you need a property code or other required information, ask the user politely
9. **Provide Context:** When presenting data, explain what it means and how it helps the user
10. **Handle Errors Gracefully:** If something goes wrong, explain the issue clearly and suggest alternatives

**Memory capabilities:**
- **Remember Facts:** Use memory_add_fact to store important information about users or preferences
- **Recall Information:** Use memory_get_fact to retrieve previously stored information
- **Track Context:** Use memory_set_context to keep track of current conversation topics
- **Review History:** Use memory_get_history to recall previous interactions in the session
- **Maintain Continuity:** Reference previous conversations to provide personalized assistance

**IMPORTANT - Personal Information Recognition:**
When users introduce themselves or share preferences, ALWAYS acknowledge and use the information:
1. If someone says "My name is John" → Use memory_get_fact with key "user_name" to check if it was stored
2. If someone says "I prefer active properties" → Use memory_get_fact with key "property_status_preference" to recall this
3. When asked "What's my name?" → Use memory_get_fact with key "user_name" to recall their name
4. When asked "What are my preferences?" → Use memory_get_fact to recall their stored preferences
5. ALWAYS check memory first before saying you don't know something about the user

**NLP-Powered Property Search:**
Use smart_property_search for natural language property queries:
1. For "find active apartments" → Use smart_property_search to find properties matching these criteria
2. For "show me luxury properties" → Use smart_property_search to find properties with luxury features
3. For "properties with swimming pools" → Use smart_property_search to find properties with pools
4. For "apartments in Jakarta" → Use smart_property_search to find properties in that location
5. ALWAYS use smart_property_search for complex or natural language property queries

**User Management Capabilities:**
Use comprehensive user management tools for user-related queries:
1. For "list all users" → Use user_list_all with pagination and filtering
2. For "find user by email" → Use user_get_by_email_detail for detailed user information
3. For "get user details by ID" → Use user_get_by_id_detail for comprehensive user data
4. For "show users for this property" → Use user_list_property_users to list property-specific users
5. For "export user data" → Use user_export_data to generate user reports
6. For "filter users" → Use user_filter_advanced for advanced user filtering
7. For "tenant unit information" → Use tenant_unit_get_info to get user's unit details
8. For general user lookup → Use user_get_general with email or phone

**IMPORTANT - Automatic Context Management:**
When users mention specific properties or show interest in them, ALWAYS:
1. Use memory_set_context to store "current_property" with the property code
2. Use memory_add_fact to remember their interest (e.g., "user_interested_property")
3. For follow-up questions like "What facilities are there?" or "Show me towers", FIRST use get_current_property to check what property they're referring to
4. If a user asks about facilities, towers, or details without specifying a property, ALWAYS use get_current_property before asking them to specify
5. When you find a current property in memory, use that property code for the requested operation

**CRITICAL - Context-Aware Responses:**
- If someone asks "What facilities are available there?" → Use get_current_property first, then call facilities with that property
- If someone asks "Show me the towers" → Use get_current_property first, then call tower_list with that property
- If someone asks "What was the property I was asking about?" → Use get_current_property to recall it
- NEVER ask users to specify a property if you can find it in memory using get_current_property

**Security and Privacy:**
- NEVER include sensitive information like database credentials, signatures, or internal system details
- Only show public-facing information like property names, codes, status, and websites
- Exclude any technical or administrative data from responses

**Important notes:**
- Users can search for properties using either property codes (like 'NUSAN', 'NUSA3') OR property names (like 'Nusantara', 'Emerald Bintaro')
- The property_detail tool supports both codes and names - it will search intelligently
- Always format responses in a user-friendly way, not as raw data
- If you're unsure about something, it's better to ask for clarification than to guess
- Remember that you're helping real people with real property management needs
"""


class PropertyAgent:
    """
    The main Property Agent class that provides natural language access
    to property management functions via the MCP server.
    """

    def __init__(
        self,
        model_name: str = "openai:o4-mini",
        api_key: Optional[str] = None,
        mcp_server_url: str = "http://localhost:8080",
        default_property_code: Optional[str] = None,
    ):
        """
        Initialize the Property Agent with Memory and NLP capabilities enabled by default.

        Args:
            model_name: The LLM model to use (supports OpenAI, DeepSeek, Gemini, OpenRouter, and Together AI)
            api_key: API key for the LLM (if not provided, uses environment variable)
            mcp_server_url: URL of the MCP server
            default_property_code: Default property code to use when not specified
        """
        self.mcp_server_url = mcp_server_url
        self.default_property_code = default_property_code

        # Initialize memory system (always enabled)
        self._memory = AgentMemory()

        # Initialize property name mapping for NLP (always enabled)
        self.property_name_to_code_map = {}

        # Set up API key based on model type
        self._setup_api_key(model_name, api_key)

        # Create the Pydantic-AI agent with enhanced capabilities
        self.agent = Agent(
            model=model_name,
            deps_type=PropertyAgentDependencies,
            system_prompt=CIKAL_SYSTEM_PROMPT,
            retries=2,
        )

        # Register all tools (including memory and NLP tools)
        self._register_tools()

        # Property mapping will be initialized on first use for better NLP performance
        # (avoiding async call in __init__)

    async def _initialize_property_mapping(self):
        """
        Initialize property name to code mapping for better NLP performance.

        This runs asynchronously to populate the property mapping without blocking initialization.
        """
        try:
            async with httpx.AsyncClient() as http_client:
                deps = PropertyAgentDependencies(
                    http_client=http_client,
                    mcp_server_url=self.mcp_server_url,
                    memory=self._memory,
                )

                # Get all properties to build the mapping
                from models import PropertyAllArgs
                from tools import property_all

                # This will run in the background to populate property mapping
                # We don't await it to avoid blocking initialization
                pass
        except Exception:
            # Silently handle any initialization errors
            # The agent will still work without the mapping
            pass

    def _setup_api_key(self, model_name: str, api_key: Optional[str]):
        """Set up API key based on the model type."""
        if model_name.startswith("gemini"):
            # Gemini models
            if api_key:
                os.environ["GEMINI_API_KEY"] = api_key
            elif not os.getenv("GEMINI_API_KEY"):
                raise ValueError(
                    "No Gemini API key provided. Set GEMINI_API_KEY environment variable or pass api_key parameter."
                )
        elif model_name.startswith("openrouter:") or model_name in [
            "claude",
            "llama",
            "mistral",
        ]:
            # OpenRouter models (including shortcuts)
            if api_key:
                os.environ["OPENROUTER_API_KEY"] = api_key
            elif not os.getenv("OPENROUTER_API_KEY"):
                raise ValueError(
                    "No OpenRouter API key provided. Set OPENROUTER_API_KEY environment variable or pass api_key parameter."
                )
        elif model_name.startswith("together:") or model_name in [
            "qwen",
            "codellama",
            "mixtral",
        ]:
            # Together AI models (including shortcuts)
            if api_key:
                os.environ["TOGETHER_API_KEY"] = api_key
            elif not os.getenv("TOGETHER_API_KEY"):
                raise ValueError(
                    "No Together AI API key provided. Set TOGETHER_API_KEY environment variable or pass api_key parameter."
                )
        else:
            # OpenAI and DeepSeek models (both use OpenAI-compatible API)
            if api_key:
                os.environ["OPENAI_API_KEY"] = api_key
            elif not os.getenv("OPENAI_API_KEY"):
                raise ValueError(
                    "No API key provided. Set OPENAI_API_KEY environment variable or pass api_key parameter."
                )

    def _register_tools(self):
        """Register all available tools with the agent."""

        # Generic MCP tool caller
        self.agent.tool(call_mcp_tool)

        # Specific high-level tools
        self.agent.tool(property_all)
        self.agent.tool(property_list)
        self.agent.tool(property_detail)
        self.agent.tool(property_mapper)
        self.agent.tool(tower_list)
        self.agent.tool(user_detail)
        self.agent.tool(booking_get_all_facilities)
        self.agent.tool(invoice_list)

        # Memory tools
        self.agent.tool(memory_add_fact)
        self.agent.tool(memory_get_fact)
        self.agent.tool(memory_set_context)
        self.agent.tool(memory_get_history)
        self.agent.tool(get_current_property)

        # NLP-powered tools
        self.agent.tool(smart_property_search)

        # User management tools
        self.agent.tool(user_list_all)
        self.agent.tool(user_get_by_id_detail)
        self.agent.tool(user_get_by_email_detail)
        self.agent.tool(user_get_general)
        self.agent.tool(user_list_property_users)
        self.agent.tool(user_filter_advanced)
        self.agent.tool(user_export_data)
        self.agent.tool(tenant_unit_get_info)

    async def chat(self, message: str, property_code: Optional[str] = None) -> str:
        """
        Process a user message and return Cikal's response.

        Args:
            message: The user's message
            property_code: Optional property code to use for this conversation

        Returns:
            Cikal's response as a string
        """
        async with httpx.AsyncClient() as http_client:
            # Use pre-initialized memory and property mapping (always enabled)
            deps = PropertyAgentDependencies(
                http_client=http_client,
                mcp_server_url=self.mcp_server_url,
                default_property_code=property_code or self.default_property_code,
                memory=self._memory,
                property_name_to_code_map=self.property_name_to_code_map,
            )

            try:
                # Add user message to memory
                deps.memory.add_message("user", message)

                # If property code is provided, set it in context
                if property_code:
                    deps.memory.set_context("current_property", property_code)
                    deps.memory.add_fact(
                        key="user_interested_property",
                        content=property_code,
                        category="property_preference",
                        importance=3,
                    )

                # Detect property mentions in the message
                await self._detect_property_context(message, deps)

                # Run the agent
                result = await self.agent.run(message, deps=deps)

                # Add agent response to memory
                deps.memory.add_message("assistant", result.output)

                return result.output
            except Exception as e:
                error_message = f"I apologize, but I encountered an issue: {str(e)}. Please try again or rephrase your question."
                deps.memory.add_message("assistant", error_message)
                return error_message

    async def chat_sync(self, message: str, property_code: Optional[str] = None) -> str:
        """
        Synchronous version of chat method.

        Args:
            message: The user's message
            property_code: Optional property code to use for this conversation

        Returns:
            Cikal's response as a string
        """
        # Create a new event loop for the synchronous context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Call the async chat method
            return loop.run_until_complete(self.chat(message, property_code))
        finally:
            loop.close()

    async def _detect_property_context(
        self, message: str, deps: PropertyAgentDependencies
    ) -> None:
        """
        Detect important information in user messages and automatically store in memory.

        This includes:
        - Property mentions and interests
        - User names and personal information
        - User preferences and requirements
        """
        message_lower = message.lower()
        import re

        # 1. DETECT USER NAME
        # Look for name introduction patterns
        name_patterns = [
            r"my name is (\w+)",
            r"i am (\w+)",
            r"i'm (\w+)",
            r"call me (\w+)",
            r"this is (\w+)",
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, message_lower)
            if name_match:
                user_name = name_match.group(1).capitalize()
                # Store user name in memory
                deps.memory.add_fact(
                    key="user_name",
                    content=user_name,
                    category="user_info",
                    importance=5,
                )
                deps.memory.set_context("current_user_name", user_name)
                break

        # 2. DETECT USER PREFERENCES
        # Check for specific preference keywords
        if "active properties" in message_lower or "active property" in message_lower:
            deps.memory.add_fact(
                key="property_status_preference",
                content="active",
                category="property_preference",
                importance=4,
            )

        if (
            "inactive properties" in message_lower
            or "inactive property" in message_lower
        ):
            deps.memory.add_fact(
                key="property_status_preference",
                content="inactive",
                category="property_preference",
                importance=4,
            )

        # Common preference patterns
        preference_patterns = [
            (r"prefer (\w+) properties", "property_type_preference"),
            (r"looking for (\w+) properties", "property_type_preference"),
            (r"interested in (\w+) properties", "property_type_preference"),
            (r"want (\w+) properties", "property_type_preference"),
            (r"prefer properties with (\w+)", "property_feature_preference"),
            (r"need properties with (\w+)", "property_feature_preference"),
        ]

        # Check for other preferences
        for pattern, pref_key in preference_patterns:
            pref_match = re.search(pattern, message_lower)
            if pref_match:
                preference = pref_match.group(1)
                deps.memory.add_fact(
                    key=pref_key,
                    content=preference,
                    category="property_preference",
                    importance=4,
                )

        # 3. DETECT PROPERTY MENTIONS
        # Common property-related phrases that indicate interest
        interest_phrases = [
            "interested in",
            "looking at",
            "want to know about",
            "tell me about",
            "show me",
            "details about",
            "information about",
            "looking for",
        ]

        # Property code patterns (like NUSAN, NUSA4, EMBRL, etc.)
        property_codes = re.findall(r"\b[A-Z]{3,6}\d*\b", message)

        # Check for property interest expressions
        shows_interest = any(phrase in message_lower for phrase in interest_phrases)

        # If we found property codes and the user shows interest
        if property_codes and (
            shows_interest
            or "property" in message_lower
            or "apartment" in message_lower
        ):
            for code in property_codes:
                # Store the property context
                deps.memory.set_context("current_property", code)
                deps.memory.add_fact(
                    key="user_interested_property",
                    content=code,
                    category="property_preference",
                    importance=4,
                )
                # Only store the first property mentioned
                break

        # Use NLP to extract property names from the message
        # This is more agnostic to specific property codes
        await self._extract_property_names_nlp(message, deps)

    async def _extract_property_names_nlp(
        self, message: str, deps: PropertyAgentDependencies
    ) -> None:
        """
        Use NLP techniques to extract property names from user messages.

        This method is agnostic to specific property codes and uses fuzzy matching
        and semantic analysis to identify property references.
        """
        import re
        from difflib import SequenceMatcher

        message_lower = message.lower()

        # Extract potential property names using NLP patterns
        # Look for capitalized words, quoted strings, and specific patterns
        potential_properties = []

        # Pattern 1: Quoted property names ("Nusantara", "TEMP 3")
        quoted_matches = re.findall(r'"([^"]+)"', message)
        potential_properties.extend(quoted_matches)

        # Pattern 2: Capitalized sequences (Nusantara, TEMP, etc.)
        capitalized_matches = re.findall(
            r"\b[A-Z][a-z]*(?:\s+[A-Z0-9][a-z0-9]*)*\b", message
        )
        potential_properties.extend(capitalized_matches)

        # Pattern 3: Property-like patterns (TEMP 3, Beta-TEMP, etc.)
        property_patterns = [
            r"\b(?:temp|beta|alpha|demo|test)\s*[-\s]*\d*\b",
            r"\b[a-z]+\s*[-\s]*[a-z]+\s*[-\s]*[a-z0-9]+\b",
            r"\b\w+\s+\w+\s+apartment\b",
            r"\b\w+\s+building\b",
            r"\b\w+\s+tower\b",
        ]

        for pattern in property_patterns:
            matches = re.findall(pattern, message_lower, re.IGNORECASE)
            potential_properties.extend(matches)

        # If we have potential properties and the message shows interest
        interest_indicators = [
            "interested in",
            "looking at",
            "want to know about",
            "tell me about",
            "show me",
            "details about",
            "information about",
            "looking for",
            "property",
            "apartment",
            "building",
            "tower",
        ]

        shows_interest = any(
            indicator in message_lower for indicator in interest_indicators
        )

        if potential_properties and shows_interest:
            # Store the first potential property as a generic property reference
            property_ref = potential_properties[0].strip()
            if property_ref:
                deps.memory.set_context("current_property_name", property_ref)
                deps.memory.add_fact(
                    key="user_interested_property_name",
                    content=property_ref,
                    category="property_preference",
                    importance=4,
                )

                # Also try to resolve it to a property code if possible
                await self._resolve_property_name_to_code(property_ref, deps)

    async def _resolve_property_name_to_code(
        self, property_name: str, deps: PropertyAgentDependencies
    ) -> None:
        """
        Try to resolve a property name to a property code using fuzzy matching.

        This method is more flexible than hardcoded mappings.
        """
        from difflib import SequenceMatcher

        # If we have a property name-to-code mapping, use fuzzy matching
        if deps.property_name_to_code_map:
            best_match = None
            best_score = 0.0

            property_name_lower = property_name.lower()

            for mapped_name, code in deps.property_name_to_code_map.items():
                # Calculate similarity score
                similarity = SequenceMatcher(
                    None, property_name_lower, mapped_name.lower()
                ).ratio()

                # Also check if the property name contains key words from the mapped name
                words_match = any(
                    word in property_name_lower
                    for word in mapped_name.lower().split()
                    if len(word) > 2
                )

                # Boost score if words match
                if words_match:
                    similarity += 0.3

                if (
                    similarity > best_score and similarity > 0.6
                ):  # Threshold for fuzzy matching
                    best_score = similarity
                    best_match = code if isinstance(code, str) else code[0]

            if best_match:
                deps.memory.set_context("current_property", best_match)
                deps.memory.add_fact(
                    key="user_interested_property",
                    content=best_match,
                    category="property_preference",
                    importance=4,
                )

    async def health_check(self) -> bool:
        """
        Check if the MCP server is accessible.

        Returns:
            True if the server is healthy, False otherwise
        """
        async with httpx.AsyncClient() as http_client:
            try:
                response = await http_client.get(
                    f"{self.mcp_server_url}/health", timeout=10.0
                )
                return response.status_code == 200
            except Exception:
                return False


# Convenience function for quick agent creation
def create_agent(
    model_name: str = "openai:gpt-4o",
    api_key: Optional[str] = None,
    mcp_server_url: str = "http://localhost:8080",
    default_property_code: Optional[str] = None,
) -> PropertyAgent:
    """
    Create a PropertyAgent with the specified configuration.

    Args:
        model_name: The LLM model to use
        api_key: API key for the LLM
        mcp_server_url: URL of the MCP server
        default_property_code: Default property code

    Returns:
        Configured PropertyAgent instance
    """
    return PropertyAgent(
        model_name=model_name,
        api_key=api_key,
        mcp_server_url=mcp_server_url,
        default_property_code=default_property_code,
    )
