"""
Example usage of the Property Agent.

This module demonstrates how to use the Property Agent programmatically
in your own applications.
"""

import asyncio
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def basic_usage_example():
    """Basic example of using the Property Agent."""
    print("🏢 Basic Property Agent Usage Example")
    print("=" * 50)

    # Create the agent
    agent = PropertyAgent(
        model_name="openai:gpt-4o",  # or "openai:deepseek-chat" for DeepSeek
        mcp_server_url="http://localhost:8080",
        default_property_code="NUSAN",  # Optional default property
    )

    # Simple conversation
    print("User: Hello, who are you?")
    response = await agent.chat("Hello, who are you?")
    print(f"Cikal: {response}")
    print()

    # Property-related query
    print("User: Can you show me some properties?")
    response = await agent.chat("Can you show me some properties?")
    print(f"Cikal: {response}")
    print()


async def property_search_example():
    """Example of searching for properties."""
    print("🔍 Property Search Example")
    print("=" * 50)

    agent = PropertyAgent()

    # Search for properties in a specific location
    queries = [
        "find properties in NUSAN",
        "show me properties with pools",
        "list all available properties",
    ]

    for query in queries:
        print(f"User: {query}")
        response = await agent.chat(query)
        print(f"Cikal: {response}")
        print()


async def facility_booking_example():
    """Example of checking facilities."""
    print("🏊 Facility Booking Example")
    print("=" * 50)

    agent = PropertyAgent(default_property_code="NUSA3")

    queries = [
        "what facilities are available?",
        "show me the badminton courts",
        "are there any swimming pools?",
    ]

    for query in queries:
        print(f"User: {query}")
        response = await agent.chat(query, property_code="NUSA3")
        print(f"Cikal: {response}")
        print()


async def user_management_example():
    """Example of user management queries."""
    print("👤 User Management Example")
    print("=" * 50)

    agent = PropertyAgent()

    queries = [
        "look <NAME_EMAIL> at property NUSAN",
        "find user with ID user_12345",
        "show me user <NAME_EMAIL>",
    ]

    for query in queries:
        print(f"User: {query}")
        response = await agent.chat(query)
        print(f"Cikal: {response}")
        print()


async def financial_example():
    """Example of financial/invoice queries."""
    print("💰 Financial Management Example")
    print("=" * 50)

    agent = PropertyAgent()

    queries = [
        "show me invoices for property NUSAN",
        "list unpaid invoices for partner 123",
        "find invoices from last month",
    ]

    for query in queries:
        print(f"User: {query}")
        response = await agent.chat(query)
        print(f"Cikal: {response}")
        print()


async def error_handling_example():
    """Example of how the agent handles errors and missing information."""
    print("⚠️  Error Handling Example")
    print("=" * 50)

    agent = PropertyAgent()

    # Queries that will likely require clarification
    queries = [
        "find some properties",  # Missing property code
        "show me facilities",  # Missing property code
        "look up a user",  # Missing user identifier
    ]

    for query in queries:
        print(f"User: {query}")
        response = await agent.chat(query)
        print(f"Cikal: {response}")
        print()


async def multi_turn_conversation_example():
    """Example of a multi-turn conversation."""
    print("💬 Multi-turn Conversation Example")
    print("=" * 50)

    agent = PropertyAgent()

    # Simulate a conversation flow
    conversation = [
        "Hello, I need help with property management",
        "I'm looking for properties in NUSAN",
        "What facilities do they have?",
        "Can you show me the swimming pool details?",
        "Thank you for your help!",
    ]

    for message in conversation:
        print(f"User: {message}")
        response = await agent.chat(message, property_code="NUSAN")
        print(f"Cikal: {response}")
        print()


async def custom_configuration_example():
    """Example of custom agent configuration."""
    print("⚙️  Custom Configuration Example")
    print("=" * 50)

    # Agent with custom settings
    agent = PropertyAgent(
        model_name="openai:gpt-4o",
        mcp_server_url="http://localhost:8080",
        default_property_code="EMBRL",
    )

    # Check if MCP server is healthy
    is_healthy = await agent.health_check()
    print(f"MCP Server Health: {'✅ Healthy' if is_healthy else '❌ Not responding'}")

    if is_healthy:
        print("User: What can you help me with?")
        response = await agent.chat("What can you help me with?")
        print(f"Cikal: {response}")
    else:
        print("⚠️  MCP server is not running, so some features won't work")
    print()


async def run_all_examples():
    """Run all examples."""
    print("🚀 Property Agent Examples")
    print("=" * 60)

    # Check for API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable not set")
        print("   Please set your API key before running examples:")
        print("   export OPENAI_API_KEY='your_api_key_here'")
        return

    examples = [
        basic_usage_example,
        property_search_example,
        facility_booking_example,
        user_management_example,
        financial_example,
        error_handling_example,
        multi_turn_conversation_example,
        custom_configuration_example,
    ]

    for i, example in enumerate(examples, 1):
        try:
            await example()
            if i < len(examples):
                input("Press Enter to continue to the next example...")
                print()
        except KeyboardInterrupt:
            print("\n👋 Examples interrupted by user")
            break
        except Exception as e:
            print(f"❌ Error in example: {e}")
            print()


def main():
    """Main entry point for examples."""
    try:
        asyncio.run(run_all_examples())
    except KeyboardInterrupt:
        print("\n👋 Examples interrupted")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
