#!/usr/bin/env python3
"""
Gradio Web Interface for Cikal Property Agent.

This module provides a modern web interface for the Cikal Property Agent
with memory and NLP capabilities always enabled.
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import List, Optional, <PERSON>ple

import gradio as gr

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


class CikalGradioApp:
    """Gradio web interface for Cikal Property Agent."""

    def __init__(self):
        """Initialize the Gradio app with the property agent."""
        try:
            self.agent = PropertyAgent()
        except ValueError as e:
            # For demo purposes, create a mock agent if no API key
            print(f"⚠️  Warning: {e}")
            print("🔧 Running in demo mode without LLM integration")
            self.agent = None
        self.conversation_history = []

    async def chat_with_agent(
        self, message: str, history: List[Tuple[str, str]]
    ) -> <PERSON><PERSON>[List[Tuple[str, str]], str]:
        """
        Process user message and return updated conversation history.

        Args:
            message: User's input message
            history: Current conversation history

        Returns:
            Tuple of (updated_history, empty_string_for_input_clear)
        """
        if not message.strip():
            return history, ""

        try:
            if self.agent is None:
                # Demo mode response
                response = self._get_demo_response(message)
            else:
                # Get response from the agent
                response = await self.agent.chat(message)

            # Add to conversation history
            history.append((message, response))

            # Store in internal history for context
            self.conversation_history = history

            return history, ""

        except Exception as e:
            error_response = f"❌ I apologize, but I encountered an error: {str(e)}. Please try again."
            history.append((message, error_response))
            return history, ""

    def _get_demo_response(self, message: str) -> str:
        """Get a demo response when running without LLM integration."""
        message_lower = message.lower()

        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            return """👋 Hello! I'm Cikal, your property management assistant.

🔧 **Demo Mode Active** - This is a demonstration of the Gradio interface.

I can help you with:
🏢 Property information and management
👥 User management and tenant information
📅 Facility bookings and availability
💰 Invoice management and payments
📊 Data export and reporting

**Note**: To enable full functionality, please set up your LLM API key (OpenAI, DeepSeek, Gemini, OpenRouter, or Together AI).

Try asking me about properties, users, or any property management task!"""

        elif "properties" in message_lower or "property" in message_lower:
            return """🏢 **Property Information** (Demo Mode)

Here are some example properties:
1. **NUSAN** - Nusantara Apartment Complex
2. **TEMP 3** - Temporary Building 3
3. **EMBRL** - Emerald Residence

**Available Features:**
- Property details and specifications
- Tower and unit information
- Facility listings
- Availability status

*Note: This is demo data. Connect your API key for real property information.*"""

        elif "user" in message_lower:
            return """👥 **User Management** (Demo Mode)

Example user operations:
- List all users with pagination
- Search users by email or phone
- Get detailed user profiles
- Export user data
- Manage tenant information

**Sample Users:**
- Dyahhhh (<EMAIL>)
- Testing User (<EMAIL>)
- Demo User (<EMAIL>)

*Note: This is demo data. Connect your API key for real user management.*"""

        else:
            return f"""🤖 **Demo Response**

You asked: "{message}"

In full mode, I would process this request using:
- 🧠 Memory system to remember our conversation
- 🔍 NLP processing to understand your intent
- 🏢 Property management tools
- 👥 User management capabilities
- 📊 Data export and reporting

**To enable full functionality:**
Set your API key environment variable:
- `OPENAI_API_KEY` for OpenAI
- `DEEPSEEK_API_KEY` for DeepSeek
- `GEMINI_API_KEY` for Gemini
- `OPENROUTER_API_KEY` for OpenRouter
- `TOGETHER_API_KEY` for Together AI

*This is a demo interface showing the Gradio integration.*"""

    def clear_conversation(self) -> Tuple[List, str]:
        """Clear the conversation history."""
        self.conversation_history = []
        # Create a new agent instance to reset memory (if available)
        if self.agent is not None:
            try:
                self.agent = PropertyAgent()
            except ValueError:
                pass  # Keep in demo mode
        return [], ""

    def get_example_queries(self) -> List[str]:
        """Get example queries for the interface."""
        return [
            "Hello, my name is John and I'm looking for an apartment",
            "Show me all available properties",
            "Find me active apartments with swimming pools",
            "List all users in the system",
            "Get user details <NAME_EMAIL>",
            "What facilities are available at NUSAN property?",
            "Show me towers for property TEMP 3",
            "Export user data in CSV format",
            "What's my name and what are my preferences?",
            "I prefer luxury properties with parking",
        ]

    def create_interface(self) -> gr.Blocks:
        """Create and configure the Gradio interface."""

        # Custom CSS for better styling
        custom_css = """
        .gradio-container {
            max-width: 1200px !important;
            margin: auto !important;
        }
        .chat-container {
            height: 600px !important;
        }
        .example-btn {
            margin: 2px !important;
            font-size: 12px !important;
        }
        .header-text {
            text-align: center;
            color: #2563eb;
            margin-bottom: 20px;
        }
        .feature-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        """

        with gr.Blocks(
            title="Cikal Property Agent", theme=gr.themes.Soft(), css=custom_css
        ) as interface:

            # Header
            gr.HTML(
                """
                <div class="header-text">
                    <h1>🏢 Cikal Property Agent</h1>
                    <p>Your AI-powered property management assistant with Memory & NLP capabilities</p>
                </div>
            """
            )

            # Main chat interface
            with gr.Row():
                with gr.Column(scale=3):
                    # Chat interface
                    chatbot = gr.Chatbot(
                        label="Chat with Cikal",
                        height=600,
                        placeholder="👋 Hello! I'm Cikal, your property management assistant. How can I help you today?",
                        show_label=True,
                        container=True,
                        bubble_full_width=False,
                    )

                    # Input area
                    with gr.Row():
                        msg_input = gr.Textbox(
                            placeholder="Type your message here... (e.g., 'Show me all properties' or 'Find user by email')",
                            label="Your Message",
                            scale=4,
                            lines=2,
                        )
                        send_btn = gr.Button("Send 📤", variant="primary", scale=1)

                    # Action buttons
                    with gr.Row():
                        clear_btn = gr.Button("Clear Chat 🗑️", variant="secondary")
                        memory_btn = gr.Button("Show Memory 🧠", variant="secondary")

                with gr.Column(scale=1):
                    # Features panel
                    gr.HTML(
                        """
                        <div class="feature-box">
                            <h3>🚀 Key Features</h3>
                            <ul>
                                <li>🧠 <strong>Memory System</strong><br/>Remembers your name and preferences</li>
                                <li>🔍 <strong>NLP Processing</strong><br/>Understands natural language</li>
                                <li>🏢 <strong>Property Management</strong><br/>Complete property operations</li>
                                <li>👥 <strong>User Management</strong><br/>Comprehensive user system</li>
                                <li>📊 <strong>Data Export</strong><br/>Reports and analytics</li>
                            </ul>
                        </div>
                    """
                    )

                    # Example queries
                    gr.Markdown("### 💡 Try These Examples:")
                    example_queries = self.get_example_queries()

                    for i, query in enumerate(example_queries):
                        btn = gr.Button(
                            query[:50] + "..." if len(query) > 50 else query,
                            elem_classes=["example-btn"],
                            size="sm",
                        )
                        btn.click(lambda q=query: q, outputs=msg_input)

            # Status and info
            with gr.Row():
                status_text = gr.Textbox(
                    label="Status",
                    value="✅ Ready - Memory and NLP enabled",
                    interactive=False,
                    max_lines=1,
                )

            # Event handlers
            def handle_send(message, history):
                """Handle send button click."""
                return asyncio.run(self.chat_with_agent(message, history))

            def handle_memory():
                """Handle memory button click."""
                return asyncio.run(
                    self.agent.chat(
                        "Show me our conversation history and what you remember about me"
                    )
                )

            # Wire up events
            send_btn.click(
                handle_send, inputs=[msg_input, chatbot], outputs=[chatbot, msg_input]
            )

            msg_input.submit(
                handle_send, inputs=[msg_input, chatbot], outputs=[chatbot, msg_input]
            )

            clear_btn.click(self.clear_conversation, outputs=[chatbot, msg_input])

            memory_btn.click(
                lambda: asyncio.run(
                    self.agent.chat(
                        "Show me our conversation history and what you remember about me"
                    )
                ),
                outputs=status_text,
            )

            # API endpoints for external access
            gr.api(self.chat_api, api_name="chat")

            gr.api(self.get_properties_api, api_name="properties")

            gr.api(self.get_users_api, api_name="users")

        return interface

    async def chat_api(self, message: str) -> str:
        """API endpoint for chat functionality."""
        try:
            if self.agent is None:
                return self._get_demo_response(message)
            response = await self.agent.chat(message)
            return response
        except Exception as e:
            return f"Error: {str(e)}"

    async def get_properties_api(self) -> str:
        """API endpoint for getting all properties."""
        try:
            if self.agent is None:
                return self._get_demo_response("Show me all properties")
            response = await self.agent.chat("Show me all properties")
            return response
        except Exception as e:
            return f"Error: {str(e)}"

    async def get_users_api(self, limit: int = 10) -> str:
        """API endpoint for getting users."""
        try:
            if self.agent is None:
                return self._get_demo_response(f"List the first {limit} users")
            response = await self.agent.chat(f"List the first {limit} users")
            return response
        except Exception as e:
            return f"Error: {str(e)}"


def main():
    """Main function to launch the Gradio app."""
    print("🚀 Starting Cikal Property Agent Gradio Interface...")

    # Create the app
    app = CikalGradioApp()
    interface = app.create_interface()

    # Launch configuration
    launch_kwargs = {
        "server_name": "0.0.0.0",  # Listen on all interfaces
        "server_port": 7860,  # Default Gradio port
        "share": False,  # Set to True for public sharing
        "debug": True,  # Enable debug mode
        "show_error": True,  # Show errors in interface
        "quiet": False,  # Show startup messages
    }

    print("🌐 Launching Gradio interface...")
    print(f"📍 Local URL: http://localhost:{launch_kwargs['server_port']}")

    if launch_kwargs.get("share"):
        print("🌍 Public URL will be generated...")

    # Launch the interface
    interface.launch(**launch_kwargs)


if __name__ == "__main__":
    main()
