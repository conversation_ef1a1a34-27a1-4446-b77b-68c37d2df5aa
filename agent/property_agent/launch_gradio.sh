#!/bin/bash

# 🚀 Cikal Property Agent - Gradio Interface Launcher
# This script launches the Gradio web interface for the Cikal Property Agent

echo "🚀 Cikal Property Agent - Gradio Interface Launcher"
echo "=================================================="

# Check if virtual environment exists
if [ ! -d "../../.venv" ]; then
    echo "❌ Virtual environment not found at ../../.venv"
    echo "Please create and activate the virtual environment first:"
    echo "  python -m venv ../../.venv"
    echo "  source ../../.venv/bin/activate"
    echo "  pip install -r ../../requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source ../../.venv/bin/activate

# Check if Gradio is installed
if ! python -c "import gradio" 2>/dev/null; then
    echo "📦 Installing Gradio..."
    pip install gradio
fi

# Check for API keys
echo "🔑 Checking API key configuration..."
if [ -n "$OPENAI_API_KEY" ]; then
    echo "✅ OpenAI API key found"
elif [ -n "$DEEPSEEK_API_KEY" ]; then
    echo "✅ DeepSeek API key found"
elif [ -n "$GEMINI_API_KEY" ]; then
    echo "✅ Gemini API key found"
elif [ -n "$OPENROUTER_API_KEY" ]; then
    echo "✅ OpenRouter API key found"
elif [ -n "$TOGETHER_API_KEY" ]; then
    echo "✅ Together AI API key found"
else
    echo "⚠️  No API key found - running in demo mode"
    echo "   Set one of these environment variables for full functionality:"
    echo "   - OPENAI_API_KEY"
    echo "   - DEEPSEEK_API_KEY"
    echo "   - GEMINI_API_KEY"
    echo "   - OPENROUTER_API_KEY"
    echo "   - TOGETHER_API_KEY"
fi

# Check if MCP server is running
echo "🔌 Checking MCP server connection..."
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ MCP server is running at http://localhost:8080"
else
    echo "⚠️  MCP server not detected at http://localhost:8080"
    echo "   Some features may not work without the MCP server"
    echo "   Start the MCP server first if you want full functionality"
fi

echo ""
echo "🌐 Starting Gradio interface..."
echo "📍 Interface will be available at: http://localhost:7860"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Launch the Gradio app
python gradio_app.py

echo ""
echo "👋 Gradio interface stopped. Thank you for using Cikal Property Agent!"
