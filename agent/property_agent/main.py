"""
Command-line interface for the Property Agent.

This module provides a CLI for interacting with <PERSON><PERSON><PERSON>, the property management agent.
It supports both interactive chat mode and single-command execution.
"""

import argparse
import asyncio
import os
import sys
from typing import Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt
from rich.text import Text

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent

console = Console()


def print_welcome():
    """Print a welcome message for Cikal."""
    welcome_text = Text()
    welcome_text.append("🏢 ", style="bold blue")
    welcome_text.append(
        "Cikal - Your Property Management Assistant", style="bold green"
    )
    welcome_text.append(" 🏢", style="bold blue")

    welcome_panel = Panel(
        welcome_text, subtitle="Powered by Pydantic-AI", border_style="blue"
    )
    console.print(welcome_panel)
    console.print()
    console.print("Hi! I'm <PERSON><PERSON><PERSON>, your friendly property management assistant.")
    console.print(
        "I can help you with properties, users, facilities, invoices, and more!"
    )
    console.print()
    console.print("💡 [bold yellow]Tips:[/bold yellow]")
    console.print("  • Try: 'find properties in NUSAN'")
    console.print("  • Try: 'show me the facilities at NUSA3'")
    console.print("  • Try: 'list invoices for property EMBRL'")
    console.print("  • Try: 'map property name TEMP 3 to its code'")
    console.print("  • Type 'exit' or 'quit' to end our conversation")
    console.print()


async def interactive_mode(agent: PropertyAgent, property_code: Optional[str] = None):
    """Run the agent in interactive chat mode."""
    print_welcome()

    # Check MCP server health
    console.print("🔍 Checking connection to property management system...")
    if await agent.health_check():
        console.print("✅ [green]Connected successfully![/green]")
    else:
        console.print(
            "⚠️  [yellow]Warning: Cannot connect to property management system.[/yellow]"
        )
        console.print("   Some features may not work properly.")
    console.print()

    # Set default property if provided
    if property_code:
        console.print(f"🏢 Using default property: [bold]{property_code}[/bold]")
        console.print()

    while True:
        try:
            # Get user input
            user_input = Prompt.ask("[bold blue]You[/bold blue]").strip()

            if user_input.lower() in ["exit", "quit", "bye"]:
                console.print()
                console.print(
                    "👋 [bold green]Cikal:[/bold green] It was a pleasure helping you today! Have a great day!"
                )
                break

            if not user_input:
                continue

            # Show thinking indicator
            with console.status("[bold green]Cikal is thinking...", spinner="dots"):
                response = await agent.chat(user_input, property_code=property_code)

            # Display Cikal's response
            console.print()
            console.print(f"🤖 [bold green]Cikal:[/bold green] {response}")
            console.print()

        except KeyboardInterrupt:
            console.print()
            console.print(
                "👋 [bold green]Cikal:[/bold green] Goodbye! Feel free to come back anytime."
            )
            break
        except Exception as e:
            console.print(f"❌ [red]Error:[/red] {e}")
            console.print()


async def single_command_mode(
    agent: PropertyAgent, command: str, property_code: Optional[str] = None
):
    """Run a single command and exit."""
    try:
        response = await agent.chat(command, property_code=property_code)
        console.print(response)
    except Exception as e:
        console.print(f"Error: {e}", style="red")
        sys.exit(1)


def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(
        description="Cikal - Property Management Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Interactive mode
  %(prog)s -c "find properties in NUSAN"     # Single command
  %(prog)s -p NUSA3 -c "show facilities"     # With default property
  %(prog)s --model deepseek                  # Use DeepSeek model
  %(prog)s --model gemini                    # Use Gemini model
  %(prog)s --model claude                    # Use Claude via OpenRouter
  %(prog)s --model llama                     # Use Llama via OpenRouter
  %(prog)s --model mistral                   # Use Mistral via OpenRouter
  %(prog)s --model qwen                      # Use Qwen via Together AI
  %(prog)s --model codellama                 # Use CodeLlama via Together AI
  %(prog)s --model mixtral                   # Use Mixtral via Together AI
        """,
    )

    parser.add_argument("-c", "--command", help="Execute a single command and exit")

    parser.add_argument("-p", "--property", help="Default property code to use")

    parser.add_argument(
        "--model",
        default="openai:gpt-4o",
        help="LLM model to use (default: openai:gpt-4o, also supports: deepseek, gemini, claude, llama, mistral, qwen, codellama, mixtral)",
    )

    parser.add_argument(
        "--api-key", help="API key for the LLM (overrides environment variable)"
    )

    parser.add_argument(
        "--mcp-url",
        default="http://localhost:8080",
        help="MCP server URL (default: http://localhost:8080)",
    )

    parser.add_argument(
        "--version", action="version", version="Cikal Property Agent 1.0.0"
    )

    args = parser.parse_args()

    # Handle model shortcuts and API key validation
    if args.model == "deepseek":
        args.model = "openai:deepseek-chat"
        if not args.api_key and not os.getenv("OPENAI_API_KEY"):
            console.print("❌ [red]Error:[/red] DeepSeek requires an API key.")
            console.print("Set OPENAI_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model == "gemini":
        args.model = "gemini-1.5-flash"
        if not args.api_key and not os.getenv("GEMINI_API_KEY"):
            console.print("❌ [red]Error:[/red] Gemini requires an API key.")
            console.print("Set GEMINI_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model.startswith("gemini"):
        # Handle full gemini model names like "gemini-1.5-pro"
        if not args.api_key and not os.getenv("GEMINI_API_KEY"):
            console.print("❌ [red]Error:[/red] Gemini models require an API key.")
            console.print("Set GEMINI_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model == "claude":
        args.model = "openrouter:anthropic/claude-3.5-sonnet"
        if not args.api_key and not os.getenv("OPENROUTER_API_KEY"):
            console.print("❌ [red]Error:[/red] Claude requires an OpenRouter API key.")
            console.print(
                "Set OPENROUTER_API_KEY environment variable or use --api-key"
            )
            sys.exit(1)
    elif args.model == "llama":
        args.model = "openrouter:meta-llama/llama-3.1-8b-instruct"
        if not args.api_key and not os.getenv("OPENROUTER_API_KEY"):
            console.print("❌ [red]Error:[/red] Llama requires an OpenRouter API key.")
            console.print(
                "Set OPENROUTER_API_KEY environment variable or use --api-key"
            )
            sys.exit(1)
    elif args.model == "mistral":
        args.model = "openrouter:mistralai/mistral-7b-instruct"
        if not args.api_key and not os.getenv("OPENROUTER_API_KEY"):
            console.print(
                "❌ [red]Error:[/red] Mistral requires an OpenRouter API key."
            )
            console.print(
                "Set OPENROUTER_API_KEY environment variable or use --api-key"
            )
            sys.exit(1)
    elif args.model.startswith("openrouter:"):
        # Handle full openrouter model names
        if not args.api_key and not os.getenv("OPENROUTER_API_KEY"):
            console.print("❌ [red]Error:[/red] OpenRouter models require an API key.")
            console.print(
                "Set OPENROUTER_API_KEY environment variable or use --api-key"
            )
            sys.exit(1)
    elif args.model == "qwen":
        args.model = "together:Qwen/Qwen2.5-7B-Instruct"
        if not args.api_key and not os.getenv("TOGETHER_API_KEY"):
            console.print("❌ [red]Error:[/red] Qwen requires a Together AI API key.")
            console.print("Set TOGETHER_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model == "codellama":
        args.model = "together:codellama/CodeLlama-7b-Instruct-hf"
        if not args.api_key and not os.getenv("TOGETHER_API_KEY"):
            console.print(
                "❌ [red]Error:[/red] CodeLlama requires a Together AI API key."
            )
            console.print("Set TOGETHER_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model == "mixtral":
        args.model = "together:mistralai/Mixtral-8x7B-Instruct-v0.1"
        if not args.api_key and not os.getenv("TOGETHER_API_KEY"):
            console.print(
                "❌ [red]Error:[/red] Mixtral requires a Together AI API key."
            )
            console.print("Set TOGETHER_API_KEY environment variable or use --api-key")
            sys.exit(1)
    elif args.model.startswith("together:"):
        # Handle full together model names
        if not args.api_key and not os.getenv("TOGETHER_API_KEY"):
            console.print("❌ [red]Error:[/red] Together AI models require an API key.")
            console.print("Set TOGETHER_API_KEY environment variable or use --api-key")
            sys.exit(1)

    try:
        # Create the agent
        agent = PropertyAgent(
            model_name=args.model,
            api_key=args.api_key,
            mcp_server_url=args.mcp_url,
            default_property_code=args.property,
        )

        # Run in appropriate mode
        if args.command:
            asyncio.run(single_command_mode(agent, args.command, args.property))
        else:
            asyncio.run(interactive_mode(agent, args.property))

    except ValueError as e:
        console.print(f"❌ [red]Configuration Error:[/red] {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        console.print(f"❌ [red]Unexpected Error:[/red] {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
