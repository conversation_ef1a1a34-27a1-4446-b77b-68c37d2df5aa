"""
MCP (Model Context Protocol) client for communicating with the PMS Gateway server.

This module handles all communication with the MCP server, including
JSON-RPC request formatting, error handling, and response parsing.
"""

import json
import uuid
from typing import Any, Dict, Optional

import httpx
from models import MCPResponse


class MCPClientError(Exception):
    """Exception raised for MCP client errors."""

    pass


class MCPClient:
    """Client for communicating with the MCP server."""

    def __init__(
        self, http_client: httpx.AsyncClient, server_url: str = "http://localhost:8080"
    ):
        self.http_client = http_client
        self.server_url = server_url
        self.jsonrpc_endpoint = f"{server_url}/"

    async def call_tool(
        self, tool_name: str, arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Call a tool on the MCP server.

        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool

        Returns:
            The tool's response data

        Raises:
            MCPClientError: If the request fails or returns an error
        """
        request_id = str(uuid.uuid4())

        payload = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {"name": tool_name, "arguments": arguments},
            "id": request_id,
        }

        try:
            response = await self.http_client.post(
                self.jsonrpc_endpoint,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30.0,
            )
            response.raise_for_status()

            response_data = response.json()
            mcp_response = MCPResponse(**response_data)

            if mcp_response.error:
                error_msg = mcp_response.error.get("message", "Unknown MCP error")
                raise MCPClientError(f"MCP server error: {error_msg}")

            return mcp_response.result or {}

        except httpx.HTTPError as e:
            raise MCPClientError(f"HTTP error calling MCP server: {e}")
        except json.JSONDecodeError as e:
            raise MCPClientError(f"Invalid JSON response from MCP server: {e}")
        except Exception as e:
            raise MCPClientError(f"Unexpected error calling MCP server: {e}")

    async def health_check(self) -> bool:
        """
        Check if the MCP server is healthy.

        Returns:
            True if the server is healthy, False otherwise
        """
        try:
            response = await self.http_client.get(
                f"{self.server_url}/health", timeout=10.0
            )
            return response.status_code == 200
        except Exception:
            return False

    async def list_tools(self) -> Dict[str, Any]:
        """
        Get the list of available tools from the MCP server.

        Returns:
            Dictionary containing available tools information
        """
        try:
            response = await self.http_client.get(
                f"{self.server_url}/tools", timeout=10.0
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise MCPClientError(f"Error listing tools: {e}")


def format_mcp_error_for_user(error: MCPClientError) -> str:
    """
    Format an MCP error for user-friendly display.

    Args:
        error: The MCP client error

    Returns:
        User-friendly error message
    """
    error_str = str(error).lower()

    if "connection" in error_str or "timeout" in error_str:
        return "I'm having trouble connecting to the property management system. Please try again in a moment."
    elif "property_code" in error_str:
        return "I need a property code to help you with that. Which property are you asking about?"
    elif "not found" in error_str:
        return "I couldn't find what you're looking for. Could you provide more details or check the information?"
    else:
        return "I encountered an issue while processing your request. Please try again or rephrase your question."
