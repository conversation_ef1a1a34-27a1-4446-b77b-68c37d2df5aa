"""
Pydantic models for the Property Agent.

This module defines the data structures used throughout the agent,
including tool arguments, responses, and dependency injection models.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from pydantic import BaseModel, Field


@dataclass
class MemoryItem:
    """A single memory item in the agent's memory."""

    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    category: str = "general"
    importance: int = 1  # 1-5 scale, higher is more important


@dataclass
class AgentMemory:
    """Short-term memory for the agent to maintain context during a session."""

    # List of recent messages (user and agent)
    conversation_history: List[Dict[str, str]] = field(default_factory=list)

    # Dictionary of key facts the agent has learned
    facts: Dict[str, MemoryItem] = field(default_factory=dict)

    # User preferences and information
    user_preferences: Dict[str, str] = field(default_factory=dict)

    # Current context (e.g., current property being discussed)
    current_context: Dict[str, Any] = field(default_factory=dict)

    # Maximum number of conversation turns to remember
    max_history_length: int = 10

    def add_message(self, role: str, content: str) -> None:
        """Add a message to the conversation history."""
        self.conversation_history.append({"role": role, "content": content})

        # Trim history if it exceeds the maximum length
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[
                -self.max_history_length :
            ]

    def add_fact(
        self, key: str, content: str, category: str = "general", importance: int = 1
    ) -> None:
        """Add a fact to the agent's memory."""
        self.facts[key] = MemoryItem(
            content=content, category=category, importance=importance
        )

    def set_context(self, key: str, value: Any) -> None:
        """Set a context value."""
        self.current_context[key] = value

    def get_context(self, key: str, default: Any = None) -> Any:
        """Get a context value."""
        return self.current_context.get(key, default)

    def set_preference(self, key: str, value: str) -> None:
        """Set a user preference."""
        self.user_preferences[key] = value

    def get_formatted_history(self, max_items: Optional[int] = None) -> str:
        """Get the conversation history formatted as a string."""
        items = self.conversation_history
        if max_items:
            items = items[-max_items:]

        result = []
        for item in items:
            role_prefix = "User: " if item["role"] == "user" else "Cikal: "
            result.append(f"{role_prefix}{item['content']}")

        return "\n\n".join(result)


@dataclass
class PropertyAgentDependencies:
    """Dependencies injected into the agent's tools and system prompts."""

    http_client: httpx.AsyncClient
    mcp_server_url: str = "http://localhost:8080"
    default_property_code: Optional[str] = None
    property_name_to_code_map: Optional[Dict[str, str]] = None
    memory: AgentMemory = field(default_factory=AgentMemory)


# Tool Argument Models
class GenericToolArgs(BaseModel):
    """Arguments for calling any MCP tool generically."""

    tool_name: str = Field(..., description="The exact name of the MCP tool to call")
    arguments: Dict[str, Any] = Field(
        default_factory=dict, description="Arguments for the tool"
    )


class PropertyListArgs(BaseModel):
    """Arguments for listing properties."""

    property_code: Optional[str] = Field(
        None, description="Property code to filter by (e.g., 'NUSAN', 'NUSA3')"
    )
    page: int = Field(1, description="Page number for pagination", ge=1)
    limit: int = Field(10, description="Number of results per page", ge=1, le=100)
    search: Optional[str] = Field(None, description="Search query to filter properties")


class UserDetailArgs(BaseModel):
    """Arguments for getting user details."""

    property_code: str = Field(..., description="Property code where the user resides")
    user_id: Optional[str] = Field(None, description="User ID to look up")
    email: Optional[str] = Field(None, description="User email to look up")


class BookingFacilitiesArgs(BaseModel):
    """Arguments for getting booking facilities."""

    property_code: str = Field(..., description="Property code to check for facilities")
    facility_type: Optional[str] = Field(
        None, description="Type of facility (e.g., 'gym', 'pool', 'court')"
    )


class InvoiceListArgs(BaseModel):
    """Arguments for listing invoices."""

    property_code: str = Field(..., description="Property code")
    partner_id: Optional[str] = Field(None, description="Partner ID to filter invoices")
    status: Optional[str] = Field(
        None, description="Invoice status filter (e.g., 'paid', 'unpaid')"
    )
    page: int = Field(1, description="Page number for pagination", ge=1)
    limit: int = Field(10, description="Number of results per page", ge=1, le=100)


class PropertyDetailArgs(BaseModel):
    """Arguments for getting property details."""

    property_identifier: str = Field(
        ...,
        description="Property code (e.g., 'NUSAN', 'NUSA3') or property name (e.g., 'Nusantara', 'Emerald Bintaro')",
    )


class PropertyAllArgs(BaseModel):
    """Arguments for getting all properties."""

    page: int = Field(1, description="Page number for pagination", ge=1)
    limit: int = Field(20, description="Number of results per page", ge=1, le=50)
    search: Optional[str] = Field(None, description="Search query to filter properties")


class TowerListArgs(BaseModel):
    """Arguments for listing towers in a property."""

    property_identifier: str = Field(
        ...,
        description="Property code (e.g., 'NUSA4', 'EMBRL') or property name (e.g., 'TEMP 3', 'Predeploy')",
    )
    page: int = Field(1, description="Page number for pagination", ge=1)
    limit: int = Field(20, description="Number of results per page", ge=1, le=50)


class PropertyMapperArgs(BaseModel):
    """Arguments for mapping property names to codes."""

    property_name: str = Field(
        ...,
        description="Property name or partial name to map to property code (e.g., 'TEMP 3', 'Nusantara', 'Emerald')",
    )


class MemoryAddFactArgs(BaseModel):
    """Arguments for adding a fact to the agent's memory."""

    key: str = Field(
        ...,
        description="Unique identifier for the fact (e.g., 'user_name', 'preferred_property')",
    )
    content: str = Field(
        ...,
        description="The content of the fact to remember",
    )
    category: str = Field(
        "general",
        description="Category of the fact (e.g., 'user_info', 'property_preference', 'general')",
    )
    importance: int = Field(
        1,
        description="Importance of the fact on a scale of 1-5, with 5 being most important",
        ge=1,
        le=5,
    )


class MemoryGetFactArgs(BaseModel):
    """Arguments for retrieving a fact from the agent's memory."""

    key: str = Field(
        ...,
        description="Unique identifier for the fact to retrieve",
    )


class MemorySetContextArgs(BaseModel):
    """Arguments for setting a context value in the agent's memory."""

    key: str = Field(
        ...,
        description="Key for the context value (e.g., 'current_property', 'current_topic')",
    )
    value: str = Field(
        ...,
        description="Value to store in the context",
    )


class MemoryGetHistoryArgs(BaseModel):
    """Arguments for retrieving conversation history."""

    max_items: Optional[int] = Field(
        None,
        description="Maximum number of conversation turns to retrieve (default: all)",
        ge=1,
    )


class GetCurrentPropertyArgs(BaseModel):
    """Arguments for getting the current property from context."""

    # No arguments needed - this tool automatically checks memory for current property


class SmartPropertySearchArgs(BaseModel):
    """Arguments for intelligent property search using NLP."""

    query: str = Field(
        ...,
        description="Natural language query for property search (e.g., 'find active apartments', 'show me luxury properties', 'properties with swimming pools')",
    )
    max_results: int = Field(
        10,
        description="Maximum number of results to return",
        ge=1,
        le=50,
    )


# Response Models
class MCPResponse(BaseModel):
    """Standard MCP server response."""

    jsonrpc: str
    id: Any  # Can be int, string, or UUID
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None


class Property(BaseModel):
    """Property information model."""

    code: str
    name: str
    address: Optional[str] = None
    description: Optional[str] = None


class User(BaseModel):
    """User information model."""

    id: str
    email: str
    name: Optional[str] = None
    property_code: Optional[str] = None


class Facility(BaseModel):
    """Facility information model."""

    id: str
    name: str
    type: str
    description: Optional[str] = None
    available: bool = True


class Invoice(BaseModel):
    """Invoice information model."""

    id: str
    partner_id: str
    amount: float
    status: str
    date: str
    description: Optional[str] = None
