# Gradio Web Interface Requirements for Cikal Property Agent
# Install with: pip install -r requirements_gradio.txt

# Core Gradio framework
gradio>=5.38.0

# Already included in main requirements but listed for completeness
# These are required for the base agent functionality
httpx>=0.24.1
pydantic>=2.0
pydantic-ai>=0.0.1
asyncio-compat>=0.1.0

# Optional: Additional UI enhancements
# pandas>=2.0.0  # For data export features (already included)
# pillow>=8.0    # For image handling (already included)
# numpy>=1.0     # For data processing (already included)

# Development and testing
# pytest>=7.0    # For testing
# black>=22.0    # For code formatting
# ruff>=0.9.3    # For linting (already included)
