"""
Setup script for the Property Agent.

This script helps users set up the Property Agent environment,
install dependencies, and configure API keys.
"""

import os
import sys
import subprocess
from pathlib import Path


def print_header(title: str):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    # Get the requirements.txt path (two levels up from this script)
    requirements_path = Path(__file__).parent.parent.parent / "requirements.txt"
    
    if not requirements_path.exists():
        print(f"❌ Requirements file not found: {requirements_path}")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_path)
        ], check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print(f"   Error output: {e.stderr}")
        return False


def setup_api_key():
    """Help user set up API key."""
    print("🔑 Setting up API key...")
    
    if os.getenv("OPENAI_API_KEY"):
        print("✅ OPENAI_API_KEY environment variable is already set")
        return True
    
    print("⚠️  OPENAI_API_KEY environment variable is not set")
    print()
    print("You need an API key to use the Property Agent.")
    print("Choose your preferred LLM provider:")
    print()
    print("1. OpenAI (GPT-4o)")
    print("   - Get API key: https://platform.openai.com/api-keys")
    print("   - Set environment variable: export OPENAI_API_KEY='your_key_here'")
    print()
    print("2. DeepSeek (Compatible with OpenAI API)")
    print("   - Get API key: https://platform.deepseek.com/")
    print("   - Set environment variable: export OPENAI_API_KEY='your_deepseek_key_here'")
    print("   - Use with: python -m main --model deepseek")
    print()
    
    choice = input("Would you like to set the API key now? (y/n): ").lower().strip()
    
    if choice == 'y':
        api_key = input("Enter your API key: ").strip()
        if api_key:
            # Add to current session
            os.environ["OPENAI_API_KEY"] = api_key
            
            # Suggest adding to shell profile
            print("✅ API key set for current session")
            print()
            print("💡 To make this permanent, add this line to your shell profile:")
            print(f"   export OPENAI_API_KEY='{api_key}'")
            print()
            print("   For bash: ~/.bashrc or ~/.bash_profile")
            print("   For zsh: ~/.zshrc")
            return True
        else:
            print("❌ No API key provided")
            return False
    else:
        print("⚠️  You'll need to set OPENAI_API_KEY before using the agent")
        return False


def check_mcp_server():
    """Check if MCP server is running."""
    print("🌐 Checking MCP server...")
    
    try:
        import httpx
        import asyncio
        
        async def check_health():
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8080/health", timeout=5.0)
                return response.status_code == 200
        
        is_healthy = asyncio.run(check_health())
        
        if is_healthy:
            print("✅ MCP server is running and healthy")
            return True
        else:
            print("⚠️  MCP server is not responding")
            return False
            
    except Exception as e:
        print("⚠️  Cannot check MCP server (this is normal if not running)")
        print(f"   Error: {e}")
        return False


def run_test():
    """Run the test script to verify setup."""
    print("🧪 Running tests to verify setup...")
    
    test_script = Path(__file__).parent / "test_agent.py"
    
    if not test_script.exists():
        print("❌ Test script not found")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, str(test_script)
        ], capture_output=True, text=True, timeout=60)
        
        print("Test output:")
        print("-" * 40)
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        print("-" * 40)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Tests timed out")
        return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def main():
    """Main setup process."""
    print_header("Property Agent Setup")
    print("Welcome! This script will help you set up the Property Agent.")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    print_header("Installing Dependencies")
    if not install_dependencies():
        print("❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Setup API key
    print_header("API Key Configuration")
    api_key_set = setup_api_key()
    
    # Check MCP server
    print_header("MCP Server Check")
    mcp_running = check_mcp_server()
    
    # Run tests if API key is set
    if api_key_set:
        print_header("Running Tests")
        test_passed = run_test()
    else:
        test_passed = False
    
    # Final summary
    print_header("Setup Summary")
    print(f"✅ Python Version: Compatible")
    print(f"✅ Dependencies: Installed")
    print(f"{'✅' if api_key_set else '⚠️ '} API Key: {'Configured' if api_key_set else 'Not set'}")
    print(f"{'✅' if mcp_running else '⚠️ '} MCP Server: {'Running' if mcp_running else 'Not running'}")
    print(f"{'✅' if test_passed else '⚠️ '} Tests: {'Passed' if test_passed else 'Failed or skipped'}")
    
    print()
    if api_key_set and test_passed:
        print("🎉 Setup completed successfully!")
        print("   You can now start the Property Agent with:")
        print("   python -m main")
    elif api_key_set:
        print("⚠️  Setup mostly complete, but some tests failed")
        print("   This is normal if the MCP server is not running")
        print("   You can still try the agent with:")
        print("   python -m main")
    else:
        print("⚠️  Setup incomplete")
        print("   Please set your API key and try again:")
        print("   export OPENAI_API_KEY='your_api_key_here'")
    
    print()
    print("📚 For more information, see agent/README.md")


if __name__ == "__main__":
    main()
