"""
Test script for the Property Agent.

This script provides basic tests to verify the agent is working correctly.
Run this after setting up your environment to ensure everything is configured properly.
"""

import asyncio
import os
import sys
from typing import Optional

# Add the parent directory to the path so we can import the agent
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from property_agent.agent import PropertyAgent


async def test_agent_initialization():
    """Test that the agent can be initialized properly."""
    print("🧪 Testing agent initialization...")

    try:
        agent = PropertyAgent(
            model_name="openai:gpt-4o", mcp_server_url="http://localhost:8080"
        )
        print("✅ Agent initialized successfully")
        return agent
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return None


async def test_mcp_server_health(agent: PropertyAgent):
    """Test MCP server connectivity."""
    print("🧪 Testing MCP server connectivity...")

    try:
        is_healthy = await agent.health_check()
        if is_healthy:
            print("✅ MCP server is healthy and accessible")
        else:
            print(
                "⚠️  MCP server is not responding (this is expected if server is not running)"
            )
        return is_healthy
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


async def test_basic_chat(agent: PropertyAgent):
    """Test basic chat functionality."""
    print("🧪 Testing basic chat functionality...")

    try:
        # Test a simple greeting
        response = await agent.chat("Hello, who are you?")
        print(f"✅ Chat response received: {response[:100]}...")

        # Test a property-related query (this will likely ask for property code)
        response = await agent.chat("Can you help me find some properties?")
        print(f"✅ Property query response: {response[:100]}...")

        return True
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False


async def test_with_property_code(agent: PropertyAgent):
    """Test functionality with a property code."""
    print("🧪 Testing with property code...")

    try:
        # Test with a property code (this will likely fail if MCP server is not running)
        response = await agent.chat(
            "Show me properties in NUSAN", property_code="NUSAN"
        )
        print(f"✅ Property-specific query response: {response[:100]}...")
        return True
    except Exception as e:
        print(
            f"⚠️  Property-specific test failed (expected if MCP server not running): {e}"
        )
        return False


async def run_all_tests():
    """Run all tests and provide a summary."""
    print("🚀 Starting Property Agent Tests")
    print("=" * 50)

    # Check for API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable not set")
        print("   Please set your OpenAI or DeepSeek API key:")
        print("   export OPENAI_API_KEY='your_api_key_here'")
        return

    print("✅ API key found")
    print()

    # Test agent initialization
    agent = await test_agent_initialization()
    if not agent:
        print("❌ Cannot proceed without a working agent")
        return
    print()

    # Test MCP server health
    mcp_healthy = await test_mcp_server_health(agent)
    print()

    # Test basic chat
    chat_works = await test_basic_chat(agent)
    print()

    # Test with property code
    property_works = await test_with_property_code(agent)
    print()

    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    print(f"✅ Agent Initialization: {'PASS' if agent else 'FAIL'}")
    print(
        f"{'✅' if mcp_healthy else '⚠️ '} MCP Server Health: {'PASS' if mcp_healthy else 'FAIL (expected if server not running)'}"
    )
    print(f"✅ Basic Chat: {'PASS' if chat_works else 'FAIL'}")
    print(
        f"{'✅' if property_works else '⚠️ '} Property Queries: {'PASS' if property_works else 'FAIL (expected if server not running)'}"
    )
    print()

    if agent and chat_works:
        print("🎉 Core functionality is working!")
        print("   You can now run the interactive agent with:")
        print("   python -m main")
        print()
        if not mcp_healthy:
            print("💡 To test full functionality:")
            print("   1. Start the MCP server on localhost:8080")
            print("   2. Run the tests again")
    else:
        print("❌ Some core functionality is not working")
        print("   Please check your configuration and try again")


def main():
    """Main entry point for the test script."""
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")


if __name__ == "__main__":
    main()
