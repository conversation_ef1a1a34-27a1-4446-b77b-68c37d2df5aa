#!/usr/bin/env python3
"""
Test script for memory functionality in Cikal Property Agent.

This script demonstrates how the agent can remember information
and maintain context during a conversation session.
"""

import asyncio
import os
import sys

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def test_memory_functionality():
    """Test the memory capabilities of the property agent."""
    print("🧠 Testing Cikal Property Agent Memory Functionality")
    print("=" * 60)
    
    # Create a single agent instance for the session
    agent = PropertyAgent()
    
    # Test conversation with memory
    conversations = [
        "Hello, my name is <PERSON> and I'm looking for an apartment",
        "I prefer active properties only",
        "What's my name?",
        "What are my preferences?",
        "Can you remember that I'm interested in TEMP 3?",
        "What property am I interested in?",
        "Show me the conversation history",
        "Tell me about the property I'm interested in"
    ]
    
    for i, message in enumerate(conversations, 1):
        print(f"\n💬 Message {i}: {message}")
        print("-" * 40)
        
        try:
            response = await agent.chat(message)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Add a small delay for readability
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Memory test completed!")


async def test_memory_tools():
    """Test the memory tools directly."""
    print("\n🛠️ Testing Memory Tools Directly")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    # Test memory tools
    memory_tests = [
        "Use memory_add_fact to remember that my name is Alice",
        "Use memory_add_fact to remember that I prefer luxury apartments",
        "Use memory_get_fact to recall my name",
        "Use memory_set_context to set current_topic to apartment_search",
        "Use memory_get_history to show our conversation"
    ]
    
    for i, test in enumerate(memory_tests, 1):
        print(f"\n🔧 Test {i}: {test}")
        print("-" * 40)
        
        try:
            response = await agent.chat(test)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Memory tools test completed!")


async def test_context_persistence():
    """Test that context persists across multiple interactions."""
    print("\n🔄 Testing Context Persistence")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    # Test context persistence
    context_tests = [
        "I'm interested in property NUSAN",
        "What facilities are available there?",
        "Can you show me the towers?",
        "What was the property I was asking about?",
        "Remember that I prefer properties with swimming pools"
    ]
    
    for i, test in enumerate(context_tests, 1):
        print(f"\n🔄 Context Test {i}: {test}")
        print("-" * 40)
        
        try:
            response = await agent.chat(test)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Context persistence test completed!")


async def main():
    """Main test function."""
    print("🚀 Cikal Property Agent - Memory System Test")
    print("=" * 60)
    
    try:
        # Test basic memory functionality
        await test_memory_functionality()
        
        # Test memory tools
        await test_memory_tools()
        
        # Test context persistence
        await test_context_persistence()
        
        print("\n🎉 All memory tests completed successfully!")
        print("\n💡 Key Features Demonstrated:")
        print("  ✅ Conversation history tracking")
        print("  ✅ Fact storage and retrieval")
        print("  ✅ Context management")
        print("  ✅ Memory tools integration")
        print("  ✅ Session persistence")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
