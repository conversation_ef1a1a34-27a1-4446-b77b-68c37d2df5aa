#!/usr/bin/env python3
"""
Test script for multi-model API integration with Cikal Property Agent.

This script demonstrates how to use different LLM models including OpenAI,
DeepSeek, Gemini, OpenRouter, and Together AI models with the property management agent.
"""

import asyncio
import os
import sys
from typing import Optional

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def test_model(model_name: str, api_key: Optional[str] = None):
    """Test a specific model configuration."""
    print(f"\n🧪 Testing {model_name}...")

    try:
        # Create agent with specified model
        agent = PropertyAgent(
            model_name=model_name,
            api_key=api_key,
            mcp_server_url="http://localhost:8080",
        )

        print(f"✅ Agent created successfully with {model_name}")

        # Test a simple query
        response = await agent.chat("Hello! What can you help me with?")
        print(f"📝 Response: {response[:100]}...")

        return True

    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Runtime Error: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Cikal Property Agent - Model Integration Test")
    print("=" * 50)

    # Test configurations
    test_configs = [
        {
            "name": "OpenAI GPT-4o",
            "model": "openai:gpt-4o",
            "env_var": "OPENAI_API_KEY",
        },
        {
            "name": "DeepSeek Chat",
            "model": "openai:deepseek-chat",
            "env_var": "OPENAI_API_KEY",
        },
        {
            "name": "Gemini 1.5 Flash",
            "model": "gemini-1.5-flash",
            "env_var": "GEMINI_API_KEY",
        },
        {
            "name": "Gemini 1.5 Pro",
            "model": "gemini-1.5-pro",
            "env_var": "GEMINI_API_KEY",
        },
        {
            "name": "Claude 3.5 Sonnet",
            "model": "openrouter:anthropic/claude-3.5-sonnet",
            "env_var": "OPENROUTER_API_KEY",
        },
        {
            "name": "Llama 3.1 8B",
            "model": "openrouter:meta-llama/llama-3.1-8b-instruct",
            "env_var": "OPENROUTER_API_KEY",
        },
        {
            "name": "Mistral 7B",
            "model": "openrouter:mistralai/mistral-7b-instruct",
            "env_var": "OPENROUTER_API_KEY",
        },
        {
            "name": "Qwen 2.5 7B",
            "model": "together:Qwen/Qwen2.5-7B-Instruct",
            "env_var": "TOGETHER_API_KEY",
        },
        {
            "name": "CodeLlama 7B",
            "model": "together:codellama/CodeLlama-7b-Instruct-hf",
            "env_var": "TOGETHER_API_KEY",
        },
        {
            "name": "Mixtral 8x7B",
            "model": "together:mistralai/Mixtral-8x7B-Instruct-v0.1",
            "env_var": "TOGETHER_API_KEY",
        },
    ]

    results = {}

    for config in test_configs:
        print(f"\n📋 Testing {config['name']}...")

        # Check if API key is available
        api_key = os.getenv(config["env_var"])
        if not api_key:
            print(f"⚠️  Skipping {config['name']} - No {config['env_var']} found")
            results[config["name"]] = "SKIPPED"
            continue

        # Test the model
        success = await test_model(config["model"], api_key)
        results[config["name"]] = "PASSED" if success else "FAILED"

    # Print summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)

    for name, result in results.items():
        emoji = "✅" if result == "PASSED" else "❌" if result == "FAILED" else "⚠️"
        print(f"{emoji} {name}: {result}")

    print("\n💡 To test Gemini models, set your GEMINI_API_KEY:")
    print("   export GEMINI_API_KEY='your_gemini_api_key_here'")
    print("\n💡 To test OpenAI/DeepSeek models, set your OPENAI_API_KEY:")
    print("   export OPENAI_API_KEY='your_openai_api_key_here'")
    print(
        "\n💡 To test OpenRouter models (Claude/Llama/Mistral), set your OPENROUTER_API_KEY:"
    )
    print("   export OPENROUTER_API_KEY='your_openrouter_api_key_here'")
    print(
        "\n💡 To test Together AI models (Qwen/CodeLlama/Mixtral), set your TOGETHER_API_KEY:"
    )
    print("   export TOGETHER_API_KEY='your_together_api_key_here'")


if __name__ == "__main__":
    asyncio.run(main())
