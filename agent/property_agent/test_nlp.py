#!/usr/bin/env python3
"""
Test script for NLP-powered property search in Cikal Property Agent.

This script demonstrates how the agent can understand natural language
queries and find properties without requiring specific property codes.
"""

import asyncio
import os
import sys

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def test_nlp_property_search():
    """Test the NLP-powered property search capabilities."""
    print("🧠 Testing Cikal Property Agent NLP Capabilities")
    print("=" * 60)
    
    # Create a single agent instance for the session
    agent = PropertyAgent()
    
    # Test various natural language queries
    nlp_queries = [
        "Hello, I'm <PERSON> and I'm looking for active apartments",
        "Find me luxury properties",
        "Show me active properties only",
        "I'm interested in apartments with swimming pools",
        "Can you find properties that are currently available?",
        "What apartments do you have in the beta category?",
        "Show me properties with TEMP in the name",
        "I prefer modern apartments",
        "What's my name and what am I looking for?",
        "Search for 'Nusantara' properties"
    ]
    
    for i, query in enumerate(nlp_queries, 1):
        print(f"\n💬 Query {i}: {query}")
        print("-" * 50)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Add a small delay for readability
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ NLP property search test completed!")


async def test_fuzzy_matching():
    """Test fuzzy matching capabilities."""
    print("\n🔍 Testing Fuzzy Matching Capabilities")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    # Test fuzzy matching queries
    fuzzy_queries = [
        "I'm interested in Nusantra",  # Misspelled Nusantara
        "Show me TEMP 3 property",
        "Tell me about Predeploy",
        "I want to see Beta properties",
        "Find apartments with 'emerald' in the name",
        "Show me properties similar to 'temp'",
        "What about the Bintaro apartment?"
    ]
    
    for i, query in enumerate(fuzzy_queries, 1):
        print(f"\n🔍 Fuzzy Test {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Fuzzy matching test completed!")


async def test_context_awareness():
    """Test context awareness with NLP."""
    print("\n🔄 Testing Context Awareness with NLP")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    # Test context-aware conversation
    context_queries = [
        "I'm interested in active properties",
        "Show me what you found",
        "Tell me more about the first one",
        "What facilities does it have?",
        "Can you show me the towers?",
        "What was I looking for again?"
    ]
    
    for i, query in enumerate(context_queries, 1):
        print(f"\n🔄 Context Test {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Context awareness test completed!")


async def test_preference_learning():
    """Test preference learning and application."""
    print("\n📚 Testing Preference Learning")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    # Test preference learning
    preference_queries = [
        "Hi, I'm Alex and I prefer luxury apartments with pools",
        "I only want to see active properties",
        "Show me properties that match my preferences",
        "What do you know about my preferences?",
        "Find me something that fits what I'm looking for",
        "Remember that I also like properties with gyms"
    ]
    
    for i, query in enumerate(preference_queries, 1):
        print(f"\n📚 Preference Test {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Preference learning test completed!")


async def main():
    """Main test function."""
    print("🚀 Cikal Property Agent - NLP & Agnostic Search Test")
    print("=" * 60)
    
    try:
        # Test NLP property search
        await test_nlp_property_search()
        
        # Test fuzzy matching
        await test_fuzzy_matching()
        
        # Test context awareness
        await test_context_awareness()
        
        # Test preference learning
        await test_preference_learning()
        
        print("\n🎉 All NLP tests completed successfully!")
        print("\n💡 Key NLP Features Demonstrated:")
        print("  ✅ Natural language property search")
        print("  ✅ Fuzzy matching for misspelled names")
        print("  ✅ Context-aware conversations")
        print("  ✅ Preference learning and application")
        print("  ✅ Agnostic property recognition")
        print("  ✅ Intelligent query understanding")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
