#!/usr/bin/env python3
"""
Test script to verify pronoun resolution and user context memory.

This script tests the exact scenario reported by the user:
1. Look up user by email
2. Ask "what units does she have?"
3. Verify the agent uses stored context instead of asking for clarification
"""

import asyncio
import os
import sys

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def test_pronoun_resolution_scenario():
    """Test the exact scenario reported by the user."""
    print("🔍 Testing Pronoun Resolution Scenario")
    print("=" * 50)
    
    # Set up a test API key
    os.environ["OPENAI_API_KEY"] = "test-key-for-demo"
    
    try:
        agent = PropertyAgent()
        
        print("📋 Scenario Test:")
        print("1. User: 'who user <NAME_EMAIL>'")
        print("2. User: 'what units she have?'")
        print("3. Expected: Agent should use stored context, not ask for clarification")
        print()
        
        # Step 1: Look up user by email
        print("👤 Step 1: Looking up user by email...")
        response1 = await agent.chat("who user <NAME_EMAIL>")
        print(f"🤖 Response 1: {response1[:150]}...")
        
        # Check if memory was populated
        print("\n🧠 Checking memory after user lookup...")
        user_id = agent._memory.get_context("current_user_id")
        user_name = agent._memory.get_context("current_user_name")
        user_email = agent._memory.get_context("current_user_email")
        
        print(f"📝 Stored user ID: {user_id}")
        print(f"📝 Stored user name: {user_name}")
        print(f"📝 Stored user email: {user_email}")
        
        # Check facts
        last_user_fact = agent._memory.facts.get("last_user_id")
        last_name_fact = agent._memory.facts.get("last_user_name")
        
        if last_user_fact:
            print(f"📚 Last user ID fact: {last_user_fact.content}")
        if last_name_fact:
            print(f"📚 Last user name fact: {last_name_fact.content}")
        
        # Step 2: Ask follow-up question with pronoun
        print("\n👥 Step 2: Asking follow-up question with pronoun...")
        response2 = await agent.chat("what units she have?")
        print(f"🤖 Response 2: {response2[:150]}...")
        
        # Analyze the response
        if "Could you please provide" in response2 or "provide me with the name or email" in response2:
            print("❌ FAILED: Agent asked for clarification instead of using stored context")
            return False
        else:
            print("✅ SUCCESS: Agent used stored context for pronoun resolution")
            return True
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False


async def test_memory_tool_instructions():
    """Test if the agent follows memory tool instructions."""
    print("\n🛠️ Testing Memory Tool Instructions")
    print("=" * 50)
    
    os.environ["OPENAI_API_KEY"] = "test-key-for-demo"
    
    try:
        agent = PropertyAgent()
        
        # Manually store some user context
        agent._memory.add_fact("last_user_id", "123", "user_context", 5)
        agent._memory.add_fact("last_user_name", "Dyahhhh", "user_context", 5)
        agent._memory.set_context("current_user_id", "123")
        agent._memory.set_context("current_user_name", "Dyahhhh")
        
        print("📝 Manually stored user context:")
        print("   - User ID: 123")
        print("   - User Name: Dyahhhh")
        
        # Test if agent can retrieve this context
        print("\n🔍 Testing memory retrieval...")
        response = await agent.chat("Use memory_get_fact to check what user ID is stored with key 'last_user_id'")
        print(f"🤖 Memory check response: {response[:100]}...")
        
        # Test pronoun resolution with stored context
        print("\n👥 Testing pronoun resolution with stored context...")
        response2 = await agent.chat("what units does she have?")
        print(f"🤖 Pronoun resolution response: {response2[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during memory tool test: {e}")
        return False


async def test_system_prompt_compliance():
    """Test if the agent follows the enhanced system prompt."""
    print("\n📋 Testing System Prompt Compliance")
    print("=" * 50)
    
    os.environ["OPENAI_API_KEY"] = "test-key-for-demo"
    
    try:
        agent = PropertyAgent()
        
        # Check if the system prompt includes our enhancements
        system_prompt = agent.agent.system_prompt
        
        print("🔍 Checking system prompt for key instructions...")
        
        checks = [
            ("CRITICAL - User Context Memory", "User context memory instructions"),
            ("PRONOUN RESOLUTION RULES", "Pronoun resolution rules"),
            ("MANDATORY MEMORY CHECK", "Mandatory memory check instructions"),
            ("tenant_unit_get_info without user_id", "Tenant unit context usage"),
            ("memory_get_fact", "Memory fact retrieval instructions")
        ]
        
        all_passed = True
        for check_text, description in checks:
            if check_text in system_prompt:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: Missing")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error during system prompt test: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Pronoun Resolution & User Context Memory Test")
    print("=" * 60)
    
    try:
        # Run all tests
        test1 = await test_system_prompt_compliance()
        test2 = await test_memory_tool_instructions()
        test3 = await test_pronoun_resolution_scenario()
        
        print("\n📊 Test Results:")
        print(f"✅ System Prompt Compliance: {'PASS' if test1 else 'FAIL'}")
        print(f"✅ Memory Tool Instructions: {'PASS' if test2 else 'FAIL'}")
        print(f"✅ Pronoun Resolution Scenario: {'PASS' if test3 else 'FAIL'}")
        
        if all([test1, test2, test3]):
            print("\n🎉 All tests passed!")
            print("\n💡 Key Enhancements Verified:")
            print("  ✅ Enhanced system prompt with pronoun resolution rules")
            print("  ✅ Mandatory memory check instructions")
            print("  ✅ User context storage in lookup tools")
            print("  ✅ tenant_unit_get_info context awareness")
            
            print("\n🔧 If the issue persists, it may be due to:")
            print("  • LLM not following system prompt instructions precisely")
            print("  • Need for more explicit tool selection guidance")
            print("  • Requirement for additional training examples")
            
            return True
        else:
            print("\n❌ Some tests failed!")
            print("The pronoun resolution system needs further refinement.")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Pronoun resolution test!")
    sys.exit(0 if success else 1)
