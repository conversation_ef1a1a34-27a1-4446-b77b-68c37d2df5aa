#!/usr/bin/env python3
"""
Test script for comprehensive user management functionality in Cikal Property Agent.

This script demonstrates all user-related MCP tools and capabilities.
"""

import asyncio
import os
import sys

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import PropertyAgent


async def test_user_listing():
    """Test user listing capabilities."""
    print("👥 Testing User Listing Capabilities")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    user_listing_queries = [
        "List all users",
        "Show me the first 5 users",
        "Find users with search term 'admin'",
        "List users sorted by name",
        "Show users created after 2023-01-01",
        "List users for property NUSAN"
    ]
    
    for i, query in enumerate(user_listing_queries, 1):
        print(f"\n👥 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ User listing test completed!")


async def test_user_details():
    """Test user detail retrieval."""
    print("\n🔍 Testing User Detail Retrieval")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    user_detail_queries = [
        "Get user details for ID 1",
        "Find user <NAME_EMAIL>",
        "Show user information for phone +1234567890",
        "Get user details by email or phone",
        "Show detailed information for user ID 123"
    ]
    
    for i, query in enumerate(user_detail_queries, 1):
        print(f"\n🔍 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ User detail test completed!")


async def test_property_users():
    """Test property-specific user listing."""
    print("\n🏢 Testing Property-Specific User Management")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    property_user_queries = [
        "Show users for property NUSAN",
        "List all users associated with this property",
        "Find users with access to the current property",
        "Show property users with admin role",
        "List tenant users for this building"
    ]
    
    for i, query in enumerate(property_user_queries, 1):
        print(f"\n🏢 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Property user test completed!")


async def test_user_filtering():
    """Test advanced user filtering."""
    print("\n🔍 Testing Advanced User Filtering")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    filtering_queries = [
        "Filter users with advanced criteria",
        "Show available user filter options",
        "Filter active users only",
        "Find users by specific criteria",
        "Show filtered user results"
    ]
    
    for i, query in enumerate(filtering_queries, 1):
        print(f"\n🔍 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ User filtering test completed!")


async def test_tenant_units():
    """Test tenant unit information."""
    print("\n🏠 Testing Tenant Unit Information")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    tenant_unit_queries = [
        "Get tenant unit information for user ID 1",
        "Show units for tenant user 123",
        "What units does user ID 456 have?",
        "Show lease information for user",
        "Get unit details for tenant"
    ]
    
    for i, query in enumerate(tenant_unit_queries, 1):
        print(f"\n🏠 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Tenant unit test completed!")


async def test_user_export():
    """Test user data export."""
    print("\n📊 Testing User Data Export")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    export_queries = [
        "Export user data in CSV format",
        "Generate user report in Excel",
        "Export all user information",
        "Create user data export",
        "Download user list as file"
    ]
    
    for i, query in enumerate(export_queries, 1):
        print(f"\n📊 Query {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ User export test completed!")


async def test_comprehensive_user_workflow():
    """Test a comprehensive user management workflow."""
    print("\n🔄 Testing Comprehensive User Management Workflow")
    print("=" * 60)
    
    agent = PropertyAgent()
    
    workflow_queries = [
        "I need to manage users for property NUSAN",
        "First, show me all users",
        "Now find user <NAME_EMAIL>",
        "Get detailed information for that user",
        "Show me their unit information",
        "List all users for this property",
        "Export the user data for reporting"
    ]
    
    for i, query in enumerate(workflow_queries, 1):
        print(f"\n🔄 Step {i}: {query}")
        print("-" * 40)
        
        try:
            response = await agent.chat(query)
            print(f"🤖 Cikal: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        await asyncio.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("✅ Comprehensive workflow test completed!")


async def main():
    """Main test function."""
    print("🚀 Cikal Property Agent - Comprehensive User Management Test")
    print("=" * 60)
    
    try:
        # Test all user management capabilities
        await test_user_listing()
        await test_user_details()
        await test_property_users()
        await test_user_filtering()
        await test_tenant_units()
        await test_user_export()
        await test_comprehensive_user_workflow()
        
        print("\n🎉 All user management tests completed successfully!")
        print("\n💡 User Management Features Demonstrated:")
        print("  ✅ User listing with pagination and filtering")
        print("  ✅ User detail retrieval by ID, email, or phone")
        print("  ✅ Property-specific user management")
        print("  ✅ Advanced user filtering capabilities")
        print("  ✅ Tenant unit information retrieval")
        print("  ✅ User data export functionality")
        print("  ✅ Comprehensive user management workflows")
        print("  ✅ Natural language user queries")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
