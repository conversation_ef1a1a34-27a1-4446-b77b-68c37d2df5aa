"""
Tool definitions for the Property Agent.

This module defines all the tools that the agent can use to interact
with the property management system via the MCP server.
"""

import json
from typing import Optional

from mcp_client import MCPClient, MCPClientError, format_mcp_error_for_user
from models import (
    BookingFacilitiesArgs,
    GenericToolArgs,
    GetCurrentPropertyArgs,
    InvoiceListArgs,
    MemoryAddFactArgs,
    MemoryGetFactArgs,
    MemoryGetHistoryArgs,
    MemorySetContextArgs,
    PropertyAgentDependencies,
    PropertyAllArgs,
    PropertyDetailArgs,
    PropertyListArgs,
    PropertyMapperArgs,
    SmartPropertySearchArgs,
    TenantUnitGetByIdArgs,
    TowerListArgs,
    UserDetailArgs,
    UserExportArgs,
    UserFilterArgs,
    UserGetArgs,
    UserGetByEmailArgs,
    UserGetByIdArgs,
    UserListArgs,
    UserListPropertyArgs,
)
from pydantic_ai import RunContext


async def call_mcp_tool(
    ctx: RunContext[PropertyAgentDependencies], args: GenericToolArgs
) -> str:
    """
    Generic function to call any tool on the PMS Gateway MCP Server.

    Use this for specific or less common tools not covered by other functions.
    You must know the exact tool_name and the required arguments.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Add default property code if not provided and available
        if "property_code" not in args.arguments and ctx.deps.default_property_code:
            args.arguments["property_code"] = ctx.deps.default_property_code

        result = await client.call_tool(args.tool_name, args.arguments)

        # Format the result for the LLM
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        else:
            return str(result)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def property_list(
    ctx: RunContext[PropertyAgentDependencies], args: PropertyListArgs
) -> str:
    """
    List properties with optional filtering and search capabilities.

    This is a high-level, user-friendly way to search for properties.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare arguments for the MCP call
        mcp_args = {"page": args.page, "limit": args.limit}

        if args.property_code:
            mcp_args["property_code"] = args.property_code
        elif ctx.deps.default_property_code:
            mcp_args["property_code"] = ctx.deps.default_property_code

        if args.search:
            mcp_args["search"] = args.search

        result = await client.call_tool("property_list", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "properties" in result:
            properties = result["properties"]
            if not properties:
                return "I didn't find any properties matching your criteria."

            response = f"I found {len(properties)} properties:\n\n"
            for prop in properties:
                response += (
                    f"• {prop.get('name', 'Unknown')} ({prop.get('code', 'N/A')})\n"
                )
                if prop.get("address"):
                    response += f"  Address: {prop['address']}\n"
                if prop.get("description"):
                    response += f"  Description: {prop['description']}\n"
                response += "\n"

            return response.strip()
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def user_detail(
    ctx: RunContext[PropertyAgentDependencies], args: UserDetailArgs
) -> str:
    """
    Get detailed information about a specific user.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare arguments for the MCP call
        mcp_args = {"property_code": args.property_code}

        if args.user_id:
            mcp_args["user_id"] = args.user_id
        elif args.email:
            mcp_args["email"] = args.email
        else:
            return "I need either a user ID or email address to look up user details."

        result = await client.call_tool("user_detail", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "user" in result:
            user = result["user"]
            response = f"User Information:\n"
            response += f"• Name: {user.get('name', 'Not provided')}\n"
            response += f"• Email: {user.get('email', 'Not provided')}\n"
            response += f"• ID: {user.get('id', 'Not provided')}\n"
            response += f"• Property: {user.get('property_code', 'Not provided')}\n"

            if user.get("additional_info"):
                response += f"• Additional Info: {user['additional_info']}\n"

            return response
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def booking_get_all_facilities(
    ctx: RunContext[PropertyAgentDependencies], args: BookingFacilitiesArgs
) -> str:
    """
    Get all available facilities for booking at a property.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare arguments for the MCP call
        mcp_args = {"property_code": args.property_code}

        if args.facility_type:
            mcp_args["facility_type"] = args.facility_type

        result = await client.call_tool("booking_get_all_facilities", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "facilities" in result:
            facilities = result["facilities"]
            if not facilities:
                return f"I didn't find any facilities at property {args.property_code}."

            response = f"Available facilities at {args.property_code}:\n\n"
            for facility in facilities:
                response += f"• {facility.get('name', 'Unknown Facility')}\n"
                response += f"  Type: {facility.get('type', 'Not specified')}\n"
                if facility.get("description"):
                    response += f"  Description: {facility['description']}\n"
                response += f"  Available: {'Yes' if facility.get('available', True) else 'No'}\n"
                response += "\n"

            return response.strip()
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def invoice_list(
    ctx: RunContext[PropertyAgentDependencies], args: InvoiceListArgs
) -> str:
    """
    List invoices for a specific partner at a property.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare arguments for the MCP call
        mcp_args = {
            "property_code": args.property_code,
            "page": args.page,
            "limit": args.limit,
        }

        if args.partner_id:
            mcp_args["partner_id"] = args.partner_id

        if args.status:
            mcp_args["status"] = args.status

        result = await client.call_tool("invoice_list", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "invoices" in result:
            invoices = result["invoices"]
            if not invoices:
                return "I didn't find any invoices matching your criteria."

            response = f"Found {len(invoices)} invoices:\n\n"
            for invoice in invoices:
                response += f"• Invoice #{invoice.get('id', 'Unknown')}\n"
                response += f"  Amount: ${invoice.get('amount', 0):.2f}\n"
                response += f"  Status: {invoice.get('status', 'Unknown')}\n"
                response += f"  Date: {invoice.get('date', 'Not specified')}\n"
                if invoice.get("description"):
                    response += f"  Description: {invoice['description']}\n"
                response += "\n"

            return response.strip()
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def property_detail(
    ctx: RunContext[PropertyAgentDependencies], args: PropertyDetailArgs
) -> str:
    """
    Get detailed information about a specific property.

    This provides comprehensive property details including name, URL, status, and configuration.
    Supports both property codes (e.g., 'NUSAN') and property names (e.g., 'Nusantara').
    Uses cached name-to-code mapping for intelligent lookups.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)
        property_identifier = args.property_identifier.strip()

        # First, try direct lookup by property code
        try:
            result = await client.call_tool(
                "property_get_by_id", {"id": property_identifier}
            )
            if isinstance(result, dict) and "data" in result:
                property_data = result["data"]
                return _format_property_details(property_data, property_identifier)
        except MCPClientError:
            pass

        # If direct lookup fails, try using cached name-to-code mapping
        if ctx.deps.property_name_to_code_map:
            property_code = _find_property_code_from_name(
                property_identifier, ctx.deps.property_name_to_code_map
            )

            if property_code:
                try:
                    result = await client.call_tool(
                        "property_get_by_id", {"id": property_code}
                    )
                    if isinstance(result, dict) and "data" in result:
                        property_data = result["data"]
                        return _format_property_details(
                            property_data, property_identifier
                        )
                except MCPClientError:
                    pass

        # If cached mapping doesn't work, fall back to search
        try:
            all_properties_result = await client.call_tool(
                "property_all",
                {
                    "page": 1,
                    "limit": 50,
                    "id_user": "system",
                    "search": property_identifier,
                },
            )

            if (
                isinstance(all_properties_result, dict)
                and "data" in all_properties_result
            ):
                properties = all_properties_result["data"]

                # Look for exact or partial name matches
                matching_property = None
                property_identifier_lower = property_identifier.lower()

                for prop in properties:
                    prop_name = prop.get("name", "").lower()
                    prop_code = prop.get("property_code", "").lower()

                    # Check for exact matches first
                    if (
                        property_identifier_lower == prop_name
                        or property_identifier_lower == prop_code
                    ):
                        matching_property = prop
                        break

                    # Check for partial matches in name
                    if property_identifier_lower in prop_name:
                        matching_property = prop
                        break

                if matching_property:
                    # Get full details using the property code
                    detail_result = await client.call_tool(
                        "property_get_by_id",
                        {"id": matching_property.get("property_code")},
                    )

                    if isinstance(detail_result, dict) and "data" in detail_result:
                        return _format_property_details(
                            detail_result["data"], property_identifier
                        )

                # If no exact match, show available options
                if properties:
                    response = f"I couldn't find an exact match for '{property_identifier}'. Here are similar properties:\n\n"
                    for i, prop in enumerate(properties[:5], 1):
                        response += f"{i}. **{prop.get('name', 'Unknown')}** (Code: {prop.get('property_code', 'N/A')})\n"
                    response += "\nPlease specify the exact property name or code you're looking for."
                    return response

        except MCPClientError:
            pass

        return f"I couldn't find a property with the identifier '{property_identifier}'. Please check the property name or code and try again."

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


def _find_property_code_from_name(
    property_name: str, name_to_code_map: dict
) -> Optional[str]:
    """
    Find property code from name using the cached mapping.

    Args:
        property_name: The property name or partial name to search for
        name_to_code_map: Dictionary mapping names/words to property codes

    Returns:
        Property code if found, None otherwise
    """
    property_name_lower = property_name.lower().strip()

    # Try exact match first
    if property_name_lower in name_to_code_map:
        result = name_to_code_map[property_name_lower]
        return result[0] if isinstance(result, list) else result

    # Try partial matches with individual words (including numbers)
    words = property_name_lower.split()
    for word in words:
        if (
            len(word) >= 1 and word in name_to_code_map
        ):  # Allow shorter words including numbers
            result = name_to_code_map[word]
            return result[0] if isinstance(result, list) else result

    # Try combinations of words for phrases like "TEMP 3"
    if len(words) > 1:
        for i in range(len(words)):
            for j in range(i + 1, len(words) + 1):
                phrase = " ".join(words[i:j])
                if phrase in name_to_code_map:
                    result = name_to_code_map[phrase]
                    return result[0] if isinstance(result, list) else result

    # Try substring matches in both directions
    for key, value in name_to_code_map.items():
        # Check if the search term is in the key or vice versa
        if (property_name_lower in key or key in property_name_lower) and len(key) > 2:
            return value[0] if isinstance(value, list) else value

    return None


def _format_property_details(property_data: dict, identifier: str) -> str:
    """Helper function to format property details consistently."""
    response = f"Property Details for '{identifier}':\n\n"
    response += f"• Name: {property_data.get('name', 'Not specified')}\n"
    response += (
        f"• Property Code: {property_data.get('property_code', 'Not specified')}\n"
    )
    response += (
        f"• Status: {'Active' if property_data.get('is_active') else 'Inactive'}\n"
    )

    # Only include URL if it exists and is not empty
    if property_data.get("url") and property_data["url"].strip():
        response += f"• Website: {property_data['url']}\n"

    # Exclude sensitive data like database credentials, signatures, etc.
    # Only include basic operational information

    return response


async def property_all(
    ctx: RunContext[PropertyAgentDependencies], args: PropertyAllArgs
) -> str:
    """
    Get all available properties/apartments.

    This provides a comprehensive list of all properties in the system and builds
    a name-to-code mapping for intelligent property lookups.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare arguments for the MCP call
        mcp_args = {
            "page": args.page,
            "limit": args.limit,
            "id_user": "system",  # Required parameter for property_all
        }

        if args.search:
            mcp_args["search"] = args.search

        result = await client.call_tool("property_all", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "data" in result:
            properties = result["data"]
            if not properties:
                return "No properties are currently available in the system."

            # Build name-to-code mapping for future lookups
            name_to_code_map = {}
            for prop in properties:
                prop_name = prop.get("name", "").strip()
                prop_code = prop.get("property_code", "").strip()

                if prop_name and prop_code:
                    # Store full name mapping
                    name_to_code_map[prop_name.lower()] = prop_code

                    # Store partial name mappings for words and phrases
                    name_words = prop_name.lower().split()

                    # Store individual words (including short ones and numbers)
                    for word in name_words:
                        if len(word) >= 1:  # Store all words including numbers
                            if word not in name_to_code_map:
                                name_to_code_map[word] = prop_code
                            # If word maps to multiple properties, store as list
                            elif isinstance(name_to_code_map[word], str):
                                name_to_code_map[word] = [
                                    name_to_code_map[word],
                                    prop_code,
                                ]
                            elif isinstance(name_to_code_map[word], list):
                                if prop_code not in name_to_code_map[word]:
                                    name_to_code_map[word].append(prop_code)

                    # Store meaningful phrases (like "TEMP 3", "Beta - TEMP")
                    if len(name_words) > 1:
                        for i in range(len(name_words)):
                            for j in range(i + 1, len(name_words) + 1):
                                phrase = " ".join(name_words[i:j])
                                if len(phrase) > 3:  # Only store meaningful phrases
                                    if phrase not in name_to_code_map:
                                        name_to_code_map[phrase] = prop_code
                                    elif isinstance(name_to_code_map[phrase], str):
                                        name_to_code_map[phrase] = [
                                            name_to_code_map[phrase],
                                            prop_code,
                                        ]
                                    elif isinstance(name_to_code_map[phrase], list):
                                        if prop_code not in name_to_code_map[phrase]:
                                            name_to_code_map[phrase].append(prop_code)

            # Store the mapping in dependencies for other tools to use
            ctx.deps.property_name_to_code_map = name_to_code_map

            response = f"Available Properties/Apartments ({len(properties)} found):\n\n"
            for i, prop in enumerate(properties, 1):
                prop_name = prop.get("name", "Unknown Property")
                prop_code = prop.get("property_code", "N/A")
                status = "Active" if prop.get("is_active") else "Inactive"

                response += (
                    f"{i}. **{prop_name}** (Code: {prop_code}, Status: {status})\n"
                )

                # Only include URL if it exists and is not empty
                if prop.get("url") and prop["url"].strip():
                    response += f"   • Website: {prop['url']}\n"

                response += "\n"

            response += "You can ask me for more details about any specific property by mentioning its name or code!"
            return response
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def tower_list(
    ctx: RunContext[PropertyAgentDependencies], args: TowerListArgs
) -> str:
    """
    List all towers in a specific property/apartment.

    This provides information about towers, buildings, or blocks within a property.
    Supports both property codes and property names.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)
        property_identifier = args.property_identifier.strip()

        # Try to resolve property name to code if needed
        property_code = property_identifier

        # First, try direct lookup by property code
        try:
            # Test if it's already a valid property code
            test_result = await client.call_tool(
                "property_get_by_id", {"id": property_identifier}
            )
            if isinstance(test_result, dict) and "data" in test_result:
                property_code = property_identifier
        except MCPClientError:
            # If direct lookup fails, try using cached name-to-code mapping
            if ctx.deps.property_name_to_code_map:
                mapped_code = _find_property_code_from_name(
                    property_identifier, ctx.deps.property_name_to_code_map
                )
                if mapped_code:
                    property_code = mapped_code

        # Now get the tower list using the resolved property code
        mcp_args = {
            "property_code": property_code,
            "page": args.page,
            "limit": args.limit,
        }

        result = await client.call_tool("tower_list", mcp_args)

        # Format the response for natural language
        if isinstance(result, dict) and "data" in result:
            towers = result["data"]
            if not towers:
                return f"No towers found for property '{property_identifier}' (Code: {property_code})."

            response = f"Towers in '{property_identifier}' (Code: {property_code}):\n\n"
            for i, tower in enumerate(towers, 1):
                response += f"{i}. **{tower.get('name', 'Unknown Tower')}**\n"

                if tower.get("id"):
                    response += f"   • Tower ID: {tower['id']}\n"

                if tower.get("description"):
                    response += f"   • Description: {tower['description']}\n"

                if tower.get("floors"):
                    response += f"   • Floors: {tower['floors']}\n"

                if tower.get("units"):
                    response += f"   • Units: {tower['units']}\n"

                response += "\n"

            response += f"Found {len(towers)} towers total."
            return response
        else:
            return json.dumps(result, indent=2)

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def property_mapper(
    ctx: RunContext[PropertyAgentDependencies], args: PropertyMapperArgs
) -> str:
    """Map property names to property codes using intelligent matching."""
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)
        property_name = args.property_name.strip()

        # If mapping is not available, build it first
        if not ctx.deps.property_name_to_code_map:
            result = await client.call_tool(
                "property_all", {"page": 1, "limit": 50, "id_user": "system"}
            )

            if isinstance(result, dict) and "data" in result:
                properties = result["data"]
                name_to_code_map = {}

                for prop in properties:
                    prop_name = prop.get("name", "").strip()
                    prop_code = prop.get("property_code", "").strip()

                    if prop_name and prop_code:
                        name_to_code_map[prop_name.lower()] = prop_code
                        name_words = prop_name.lower().split()

                        for word in name_words:
                            if len(word) >= 1:
                                if word not in name_to_code_map:
                                    name_to_code_map[word] = prop_code
                                elif isinstance(name_to_code_map[word], str):
                                    name_to_code_map[word] = [
                                        name_to_code_map[word],
                                        prop_code,
                                    ]
                                elif isinstance(name_to_code_map[word], list):
                                    if prop_code not in name_to_code_map[word]:
                                        name_to_code_map[word].append(prop_code)

                        if len(name_words) > 1:
                            for i in range(len(name_words)):
                                for j in range(i + 1, len(name_words) + 1):
                                    phrase = " ".join(name_words[i:j])
                                    if len(phrase) > 3:
                                        if phrase not in name_to_code_map:
                                            name_to_code_map[phrase] = prop_code

                ctx.deps.property_name_to_code_map = name_to_code_map

        # Find the property code
        if ctx.deps.property_name_to_code_map:
            property_code = _find_property_code_from_name(
                property_name, ctx.deps.property_name_to_code_map
            )

            if property_code:
                try:
                    verify_result = await client.call_tool(
                        "property_get_by_id", {"id": property_code}
                    )
                    if isinstance(verify_result, dict) and "data" in verify_result:
                        property_data = verify_result["data"]
                        actual_name = property_data.get("name", "Unknown")
                        status = (
                            "Active" if property_data.get("is_active") else "Inactive"
                        )

                        return (
                            f"✅ **Property Mapping Found**\n\n"
                            f"• **Search Term:** '{property_name}'\n"
                            f"• **Property Code:** {property_code}\n"
                            f"• **Full Name:** {actual_name}\n"
                            f"• **Status:** {status}\n\n"
                            f"You can now use the property code '{property_code}' with other tools."
                        )
                except MCPClientError:
                    return f"⚠️ Found mapping '{property_name}' -> '{property_code}' but verification failed."

        return f"❌ **No Mapping Found**\n\nCould not find a property matching '{property_name}'."

    except MCPClientError as e:
        return format_mcp_error_for_user(e)


async def memory_add_fact(
    ctx: RunContext[PropertyAgentDependencies], args: MemoryAddFactArgs
) -> str:
    """
    Add a fact to the agent's memory for later recall.

    This allows the agent to remember important information about the user or conversation.
    """
    try:
        # Add the fact to memory
        ctx.deps.memory.add_fact(
            key=args.key,
            content=args.content,
            category=args.category,
            importance=args.importance,
        )

        return f"✅ I'll remember that {args.key}: {args.content}"
    except Exception as e:
        return f"❌ I couldn't store that in my memory: {str(e)}"


async def memory_get_fact(
    ctx: RunContext[PropertyAgentDependencies], args: MemoryGetFactArgs
) -> str:
    """
    Retrieve a previously stored fact from the agent's memory.

    This allows the agent to recall important information about the user or conversation.
    """
    try:
        # Special handling for current property context
        if args.key == "current_property":
            # First check context
            current_property = ctx.deps.memory.get_context("current_property")
            if current_property:
                return f"📝 current_property: {current_property}"

            # Then check facts
            fact = ctx.deps.memory.facts.get("user_interested_property")
            if fact:
                return f"📝 current_property: {fact.content}"

            return "❓ I don't have any information about the current property in my memory."

        # Regular fact retrieval
        fact = ctx.deps.memory.facts.get(args.key)

        if fact:
            return f"📝 {args.key}: {fact.content}"
        else:
            return f"❓ I don't have any information about '{args.key}' in my memory."
    except Exception as e:
        return f"❌ I couldn't retrieve that from my memory: {str(e)}"


async def memory_set_context(
    ctx: RunContext[PropertyAgentDependencies], args: MemorySetContextArgs
) -> str:
    """
    Set a context value in the agent's memory.

    This allows the agent to keep track of the current conversation context.
    """
    try:
        # Set the context value
        ctx.deps.memory.set_context(args.key, args.value)

        return f"✅ I've updated the conversation context: {args.key} = {args.value}"
    except Exception as e:
        return f"❌ I couldn't update the context: {str(e)}"


async def memory_get_history(
    ctx: RunContext[PropertyAgentDependencies], args: MemoryGetHistoryArgs
) -> str:
    """
    Retrieve the conversation history from the agent's memory.

    This allows the agent to recall previous interactions in the current session.
    """
    try:
        # Get the formatted history
        history = ctx.deps.memory.get_formatted_history(args.max_items)

        if history:
            return f"📝 Conversation History:\n\n{history}"
        else:
            return "📝 No conversation history available yet."
    except Exception as e:
        return f"❌ I couldn't retrieve the conversation history: {str(e)}"


async def get_current_property(
    ctx: RunContext[PropertyAgentDependencies], args: GetCurrentPropertyArgs
) -> str:
    """
    Get the current property from memory context.

    This tool helps the agent remember which property the user is currently discussing
    when they ask follow-up questions like "What facilities are there?" or "Show me towers".
    """
    try:
        # First check context
        current_property = ctx.deps.memory.get_context("current_property")
        if current_property:
            return f"🏢 Current property: {current_property}"

        # Then check facts for user interested property
        fact = ctx.deps.memory.facts.get("user_interested_property")
        if fact:
            # Also set it in context for future use
            ctx.deps.memory.set_context("current_property", fact.content)
            return f"🏢 Current property: {fact.content}"

        return "❓ No current property found in memory. Please specify which property you're interested in."
    except Exception as e:
        return f"❌ I couldn't retrieve the current property: {str(e)}"


async def smart_property_search(
    ctx: RunContext[PropertyAgentDependencies], args: SmartPropertySearchArgs
) -> str:
    """
    Intelligent property search using NLP to understand user queries.

    This tool can understand natural language queries like:
    - "find active apartments"
    - "show me luxury properties"
    - "properties with swimming pools"
    - "apartments in Jakarta"
    """
    try:
        import re
        from difflib import SequenceMatcher

        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)
        query_lower = args.query.lower()

        # Extract search criteria from natural language query
        search_criteria = _extract_search_criteria_nlp(query_lower)

        # Get all properties first
        all_properties_result = await client.call_tool(
            "property_all",
            {
                "page": 1,
                "limit": 50,
                "id_user": "system",
            },
        )

        if (
            not isinstance(all_properties_result, dict)
            or "data" not in all_properties_result
        ):
            return "❌ Could not retrieve properties for search."

        properties = all_properties_result["data"]

        # Filter properties based on extracted criteria
        filtered_properties = _filter_properties_by_criteria(
            properties, search_criteria, query_lower
        )

        # Limit results
        filtered_properties = filtered_properties[: args.max_results]

        if not filtered_properties:
            return f"❓ No properties found matching your query: '{args.query}'"

        # Format results
        response = f"🔍 Found {len(filtered_properties)} properties matching '{args.query}':\n\n"

        for i, prop in enumerate(filtered_properties, 1):
            prop_name = prop.get("name", "Unknown Property")
            prop_code = prop.get("property_code", "N/A")
            status = "Active" if prop.get("is_active") else "Inactive"

            response += f"{i}. **{prop_name}** (Code: {prop_code})\n"
            response += f"   • Status: {status}\n"

            if prop.get("url") and prop["url"].strip():
                response += f"   • Website: {prop['url']}\n"

            response += "\n"

        response += "💡 Ask me for more details about any specific property!"
        return response

    except Exception as e:
        return f"❌ Search failed: {str(e)}"


def _extract_search_criteria_nlp(query: str) -> dict:
    """
    Extract search criteria from natural language query using NLP patterns.

    Returns a dictionary with extracted criteria.
    """
    criteria = {
        "status": None,
        "keywords": [],
        "property_type": None,
        "features": [],
        "location": None,
    }

    # Status patterns
    if any(word in query for word in ["active", "available", "open"]):
        criteria["status"] = "active"
    elif any(word in query for word in ["inactive", "closed", "unavailable"]):
        criteria["status"] = "inactive"

    # Property type patterns
    if any(word in query for word in ["apartment", "apartments"]):
        criteria["property_type"] = "apartment"
    elif any(word in query for word in ["building", "buildings"]):
        criteria["property_type"] = "building"
    elif any(word in query for word in ["tower", "towers"]):
        criteria["property_type"] = "tower"

    # Feature patterns
    features_map = {
        "luxury": ["luxury", "premium", "high-end", "upscale"],
        "swimming_pool": ["swimming pool", "pool", "pools"],
        "gym": ["gym", "fitness", "exercise"],
        "parking": ["parking", "garage"],
        "security": ["security", "guard", "safe"],
        "garden": ["garden", "park", "green"],
    }

    for feature, keywords in features_map.items():
        if any(keyword in query for keyword in keywords):
            criteria["features"].append(feature)

    # Extract potential keywords (capitalized words, quoted strings)
    import re

    # Quoted strings
    quoted_keywords = re.findall(r'"([^"]+)"', query)
    criteria["keywords"].extend(quoted_keywords)

    # Capitalized words (potential property names)
    capitalized_words = re.findall(r"\b[A-Z][a-z]+\b", query)
    criteria["keywords"].extend(capitalized_words)

    # Common property name patterns
    property_patterns = [
        r"\b(?:temp|beta|alpha|demo|test)\s*\d*\b",
        r"\b\w+\s+\w+\s+apartment\b",
        r"\b\w+\s+building\b",
    ]

    for pattern in property_patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        criteria["keywords"].extend(matches)

    return criteria


def _filter_properties_by_criteria(
    properties: list, criteria: dict, original_query: str
) -> list:
    """
    Filter properties based on extracted criteria and fuzzy matching.
    """
    from difflib import SequenceMatcher

    filtered = []

    for prop in properties:
        score = 0
        prop_name = prop.get("name", "").lower()
        prop_code = prop.get("property_code", "").lower()

        # Status filtering
        if criteria["status"]:
            prop_active = prop.get("is_active", False)
            if criteria["status"] == "active" and prop_active:
                score += 10
            elif criteria["status"] == "inactive" and not prop_active:
                score += 10
            elif criteria["status"] == "active" and not prop_active:
                continue  # Skip inactive properties if looking for active

        # Keyword matching with fuzzy search
        for keyword in criteria["keywords"]:
            keyword_lower = keyword.lower()

            # Exact match in name or code
            if keyword_lower in prop_name or keyword_lower in prop_code:
                score += 15

            # Fuzzy match in name
            name_similarity = SequenceMatcher(None, keyword_lower, prop_name).ratio()
            if name_similarity > 0.6:
                score += int(name_similarity * 10)

            # Word-level matching
            if any(
                word in prop_name for word in keyword_lower.split() if len(word) > 2
            ):
                score += 5

        # Direct query matching
        query_similarity = SequenceMatcher(
            None, original_query.lower(), prop_name
        ).ratio()
        if query_similarity > 0.3:
            score += int(query_similarity * 8)

        # Property type matching
        if criteria["property_type"]:
            if criteria["property_type"] in prop_name:
                score += 8

        # Add property if it has any relevance
        if score > 0 or not criteria["keywords"]:  # Include all if no specific keywords
            filtered.append((prop, score))

    # Sort by score (descending) and return properties
    filtered.sort(key=lambda x: x[1], reverse=True)
    return [prop for prop, score in filtered]


# User Management Tools
async def user_list_all(
    ctx: RunContext[PropertyAgentDependencies], args: UserListArgs
) -> str:
    """
    List all users with pagination and filtering capabilities.

    This tool provides comprehensive user listing with search, filtering, and pagination.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        # Prepare parameters for the MCP call
        params = {
            "page": args.page,
            "limit": args.limit,
            "sort_by": args.sort_by,
            "order": args.order,
        }

        # Add optional parameters
        if args.property_code:
            params["property_code"] = args.property_code
        if args.search:
            params["search"] = args.search
        if args.start_date:
            params["start_date"] = args.start_date
        if args.end_date:
            params["end_date"] = args.end_date
        if args.property_codes:
            params["property_codes"] = args.property_codes

        result = await client.call_tool("user_list", params)

        if not isinstance(result, dict):
            return "❌ Invalid response format from user list service."

        if "data" not in result:
            return "❌ No user data found in response."

        users = result["data"]
        total = result.get("total", len(users))
        current_page = result.get("current_page", args.page)
        total_pages = result.get("total_pages", 1)

        if not users:
            return "📝 No users found matching your criteria."

        # Format the response
        response = f"👥 **User List** (Page {current_page} of {total_pages}, Total: {total})\n\n"

        for i, user in enumerate(users, 1):
            user_id = user.get("id", "N/A")
            name = user.get("name", "Unknown")
            email = user.get("email", "N/A")
            phone = user.get("phone", "N/A")
            status = "Active" if user.get("is_active", True) else "Inactive"
            created_at = user.get("created_at", "N/A")

            response += f"{i}. **{name}** (ID: {user_id})\n"
            response += f"   • Email: {email}\n"
            response += f"   • Phone: {phone}\n"
            response += f"   • Status: {status}\n"

            if created_at != "N/A":
                response += f"   • Created: {created_at}\n"

            response += "\n"

        response += f"💡 Use `user_get_by_id` or `user_get_by_email` for detailed user information."
        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve users: {str(e)}"


async def user_get_by_id_detail(
    ctx: RunContext[PropertyAgentDependencies], args: UserGetByIdArgs
) -> str:
    """
    Get detailed information about a user by their ID.

    This tool retrieves comprehensive user information including profile details.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {"id": args.user_id}
        if args.property_code:
            params["property_code"] = args.property_code

        result = await client.call_tool("user_get_by_id", params)

        if not isinstance(result, dict):
            return f"❌ Invalid response format for user ID {args.user_id}."

        if "data" not in result:
            return f"❌ User with ID {args.user_id} not found."

        user = result["data"]

        # Format detailed user information
        response = f"👤 **User Details** (ID: {args.user_id})\n\n"

        # Basic information
        response += f"**Basic Information:**\n"
        response += f"• Name: {user.get('name', 'N/A')}\n"
        response += f"• Email: {user.get('email', 'N/A')}\n"
        response += f"• Phone: {user.get('phone', 'N/A')}\n"
        response += (
            f"• Status: {'Active' if user.get('is_active', True) else 'Inactive'}\n"
        )

        # Additional details if available
        if user.get("created_at"):
            response += f"• Created: {user['created_at']}\n"
        if user.get("updated_at"):
            response += f"• Updated: {user['updated_at']}\n"

        # Profile information
        if any(key in user for key in ["address", "city", "country", "postal_code"]):
            response += f"\n**Address Information:**\n"
            if user.get("address"):
                response += f"• Address: {user['address']}\n"
            if user.get("city"):
                response += f"• City: {user['city']}\n"
            if user.get("country"):
                response += f"• Country: {user['country']}\n"
            if user.get("postal_code"):
                response += f"• Postal Code: {user['postal_code']}\n"

        # Property associations
        if user.get("properties"):
            response += f"\n**Associated Properties:**\n"
            for prop in user["properties"]:
                response += (
                    f"• {prop.get('name', 'Unknown')} ({prop.get('code', 'N/A')})\n"
                )

        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve user details: {str(e)}"


async def user_get_by_email_detail(
    ctx: RunContext[PropertyAgentDependencies], args: UserGetByEmailArgs
) -> str:
    """
    Get detailed information about a user by their email address.

    This tool retrieves comprehensive user information using email as the identifier.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {"email": args.email}
        if args.property_code:
            params["property_code"] = args.property_code

        result = await client.call_tool("user_get_by_email", params)

        if not isinstance(result, dict):
            return f"❌ Invalid response format for email {args.email}."

        if "data" not in result:
            return f"❌ User with email {args.email} not found."

        user = result["data"]

        # Format detailed user information
        response = f"👤 **User Details** (Email: {args.email})\n\n"

        # Basic information
        response += f"**Basic Information:**\n"
        response += f"• ID: {user.get('id', 'N/A')}\n"
        response += f"• Name: {user.get('name', 'N/A')}\n"
        response += f"• Phone: {user.get('phone', 'N/A')}\n"
        response += (
            f"• Status: {'Active' if user.get('is_active', True) else 'Inactive'}\n"
        )

        # Additional details if available
        if user.get("created_at"):
            response += f"• Created: {user['created_at']}\n"
        if user.get("updated_at"):
            response += f"• Updated: {user['updated_at']}\n"

        # Profile information
        if any(key in user for key in ["address", "city", "country", "postal_code"]):
            response += f"\n**Address Information:**\n"
            if user.get("address"):
                response += f"• Address: {user['address']}\n"
            if user.get("city"):
                response += f"• City: {user['city']}\n"
            if user.get("country"):
                response += f"• Country: {user['country']}\n"
            if user.get("postal_code"):
                response += f"• Postal Code: {user['postal_code']}\n"

        # Property associations
        if user.get("properties"):
            response += f"\n**Associated Properties:**\n"
            for prop in user["properties"]:
                response += (
                    f"• {prop.get('name', 'Unknown')} ({prop.get('code', 'N/A')})\n"
                )

        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve user details: {str(e)}"


async def user_get_general(
    ctx: RunContext[PropertyAgentDependencies], args: UserGetArgs
) -> str:
    """
    Get user information by email or phone.

    This tool provides flexible user lookup using either email or phone number.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {}
        if args.property_code:
            params["property_code"] = args.property_code
        if args.email:
            params["email"] = args.email
        if args.phone:
            params["phone"] = args.phone

        if not args.email and not args.phone:
            return (
                "❌ Please provide either email or phone number to search for a user."
            )

        result = await client.call_tool("user_get", params)

        if not isinstance(result, dict):
            return "❌ Invalid response format from user service."

        if "data" not in result:
            search_criteria = []
            if args.email:
                search_criteria.append(f"email: {args.email}")
            if args.phone:
                search_criteria.append(f"phone: {args.phone}")
            return f"❌ User not found with {', '.join(search_criteria)}."

        user = result["data"]

        # Format user information
        response = f"👤 **User Information**\n\n"

        response += f"• ID: {user.get('id', 'N/A')}\n"
        response += f"• Name: {user.get('name', 'N/A')}\n"
        response += f"• Email: {user.get('email', 'N/A')}\n"
        response += f"• Phone: {user.get('phone', 'N/A')}\n"
        response += (
            f"• Status: {'Active' if user.get('is_active', True) else 'Inactive'}\n"
        )

        if user.get("created_at"):
            response += f"• Created: {user['created_at']}\n"

        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve user information: {str(e)}"


async def user_list_property_users(
    ctx: RunContext[PropertyAgentDependencies], args: UserListPropertyArgs
) -> str:
    """
    List users associated with a specific property.

    This tool shows all users who have access to or are associated with a particular property.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {
            "email": args.email,
            "phone": args.phone,
            "page": args.page,
            "limit": args.limit,
            "sort_by": args.sort_by,
            "order": args.order,
        }

        if args.property_code:
            params["property_code"] = args.property_code
        if args.search:
            params["search"] = args.search

        result = await client.call_tool("user_list_property", params)

        if not isinstance(result, dict):
            return "❌ Invalid response format from property user list service."

        if "data" not in result:
            return "❌ No user data found for this property."

        users = result["data"]
        total = result.get("total", len(users))
        current_page = result.get("current_page", args.page)
        total_pages = result.get("total_pages", 1)

        if not users:
            return "📝 No users found for this property."

        # Format the response
        response = f"👥 **Property Users** (Page {current_page} of {total_pages}, Total: {total})\n\n"

        for i, user in enumerate(users, 1):
            user_id = user.get("id", "N/A")
            name = user.get("name", "Unknown")
            email = user.get("email", "N/A")
            phone = user.get("phone", "N/A")
            role = user.get("role", "N/A")
            status = "Active" if user.get("is_active", True) else "Inactive"

            response += f"{i}. **{name}** (ID: {user_id})\n"
            response += f"   • Email: {email}\n"
            response += f"   • Phone: {phone}\n"
            response += f"   • Role: {role}\n"
            response += f"   • Status: {status}\n\n"

        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve property users: {str(e)}"


async def user_filter_advanced(
    ctx: RunContext[PropertyAgentDependencies], args: UserFilterArgs
) -> str:
    """
    Filter users with advanced criteria.

    This tool provides advanced filtering capabilities for user searches.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {}
        if args.property_code:
            params["property_code"] = args.property_code

        result = await client.call_tool("user_filter", params)

        if not isinstance(result, dict):
            return "❌ Invalid response format from user filter service."

        # The filter endpoint might return filter options or filtered results
        if "filters" in result:
            # Return available filter options
            filters = result["filters"]
            response = "🔍 **Available User Filters:**\n\n"

            for filter_name, filter_options in filters.items():
                response += f"**{filter_name.replace('_', ' ').title()}:**\n"
                if isinstance(filter_options, list):
                    for option in filter_options:
                        response += f"  • {option}\n"
                else:
                    response += f"  • {filter_options}\n"
                response += "\n"

            return response

        elif "data" in result:
            # Return filtered user results
            users = result["data"]
            total = result.get("total", len(users))

            if not users:
                return "📝 No users found matching the filter criteria."

            response = f"👥 **Filtered Users** (Total: {total})\n\n"

            for i, user in enumerate(users, 1):
                name = user.get("name", "Unknown")
                email = user.get("email", "N/A")
                status = "Active" if user.get("is_active", True) else "Inactive"

                response += f"{i}. **{name}**\n"
                response += f"   • Email: {email}\n"
                response += f"   • Status: {status}\n\n"

            return response

        else:
            return "❌ Unexpected response format from user filter service."

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to filter users: {str(e)}"


async def user_export_data(
    ctx: RunContext[PropertyAgentDependencies], args: UserExportArgs
) -> str:
    """
    Export user data in various formats.

    This tool allows exporting user data for reporting and analysis purposes.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {"format": args.format}
        if args.property_code:
            params["property_code"] = args.property_code

        result = await client.call_tool("user_export", params)

        if not isinstance(result, dict):
            return "❌ Invalid response format from user export service."

        if "success" in result and result["success"]:
            response = f"✅ **User Data Export Successful**\n\n"
            response += f"• Format: {args.format.upper()}\n"

            if "file_url" in result:
                response += f"• Download URL: {result['file_url']}\n"
            if "file_name" in result:
                response += f"• File Name: {result['file_name']}\n"
            if "total_records" in result:
                response += f"• Total Records: {result['total_records']}\n"
            if "export_date" in result:
                response += f"• Export Date: {result['export_date']}\n"

            response += "\n💡 The exported file contains user data according to your current access permissions."
            return response

        else:
            error_msg = result.get("message", "Unknown error occurred during export")
            return f"❌ Export failed: {error_msg}"

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to export user data: {str(e)}"


async def tenant_unit_get_info(
    ctx: RunContext[PropertyAgentDependencies], args: TenantUnitGetByIdArgs
) -> str:
    """
    Get tenant unit information by user ID.

    This tool retrieves information about units associated with a specific tenant/user.
    """
    try:
        client = MCPClient(ctx.deps.http_client, ctx.deps.mcp_server_url)

        params = {"user_id": args.user_id}
        if args.property_code:
            params["property_code"] = args.property_code

        result = await client.call_tool("tenant_unit_get_by_id", params)

        if not isinstance(result, dict):
            return f"❌ Invalid response format for user ID {args.user_id}."

        if "data" not in result:
            return f"❌ No tenant unit information found for user ID {args.user_id}."

        tenant_units = result["data"]

        if not tenant_units:
            return f"📝 No units found for user ID {args.user_id}."

        # Format tenant unit information
        response = f"🏠 **Tenant Unit Information** (User ID: {args.user_id})\n\n"

        if isinstance(tenant_units, list):
            for i, unit in enumerate(tenant_units, 1):
                response += f"**Unit {i}:**\n"
                response += f"• Unit ID: {unit.get('unit_id', 'N/A')}\n"
                response += f"• Unit Number: {unit.get('unit_number', 'N/A')}\n"
                response += f"• Tower: {unit.get('tower', 'N/A')}\n"
                response += f"• Floor: {unit.get('floor', 'N/A')}\n"
                response += f"• Type: {unit.get('unit_type', 'N/A')}\n"
                response += f"• Status: {unit.get('status', 'N/A')}\n"

                if unit.get("lease_start"):
                    response += f"• Lease Start: {unit['lease_start']}\n"
                if unit.get("lease_end"):
                    response += f"• Lease End: {unit['lease_end']}\n"

                response += "\n"
        else:
            # Single unit
            unit = tenant_units
            response += f"• Unit ID: {unit.get('unit_id', 'N/A')}\n"
            response += f"• Unit Number: {unit.get('unit_number', 'N/A')}\n"
            response += f"• Tower: {unit.get('tower', 'N/A')}\n"
            response += f"• Floor: {unit.get('floor', 'N/A')}\n"
            response += f"• Type: {unit.get('unit_type', 'N/A')}\n"
            response += f"• Status: {unit.get('status', 'N/A')}\n"

            if unit.get("lease_start"):
                response += f"• Lease Start: {unit['lease_start']}\n"
            if unit.get("lease_end"):
                response += f"• Lease End: {unit['lease_end']}\n"

        return response

    except MCPClientError as e:
        return format_mcp_error_for_user(e)
    except Exception as e:
        return f"❌ Failed to retrieve tenant unit information: {str(e)}"
